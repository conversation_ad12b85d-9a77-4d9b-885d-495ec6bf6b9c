/**
 * @file src/main/ipc/project-handlers.ts
 * @description IPC handlers for project management operations.
 * Handles project CRUD operations and project file management.
 */

import { ipcMain, dialog } from 'electron';
import { basename } from 'path';
import { sqliteService } from '../services/sqlite';
import { projectFilesService } from '../services/project-files';
import { WindowManager } from '../managers/window-manager';

export class ProjectHandlers {
  private windowManager: WindowManager;

  constructor(windowManager: WindowManager) {
    this.windowManager = windowManager;
    this.registerHandlers();
  }

  private registerHandlers(): void {
    // Project management handlers
    ipcMain.handle('projects:getAll', async () => {
      return sqliteService.getAllProjects();
    });

    ipcMain.handle('projects:add', async (_, projectPath: string) => {
      console.log('[ProjectHandlers] Adding project:', projectPath);
      try {
        const name = basename(projectPath);
        console.log('[ProjectHandlers] Project name:', name);

        // Check for duplicates
        if (sqliteService.getProjectByName(name)) {
          throw new Error(`Project with name "${name}" already exists.`);
        }

        sqliteService.addProject(name, projectPath);
        const newProject = sqliteService.getProjectByName(name);
        console.log('[ProjectHandlers] Project added successfully:', newProject);
        return newProject;
      } catch (error: any) {
        console.error('[ProjectHandlers] Failed to add project:', error);
        const mainWindow = this.windowManager.getMainWindow();
        if (mainWindow) {
          dialog.showErrorBox('Error Adding Project', error.message);
        }
        return null;
      }
    });

    ipcMain.handle('projects:delete', async (_, id: number) => {
      sqliteService.deleteProject(id);
    });

    // Project files handlers
    ipcMain.handle('projectFiles:addSelected', async (_, payload: { 
      filePaths: string[]; 
      projectId: number; 
      copyFiles?: boolean 
    }) => {
      try {
        return await projectFilesService.addSelectedFiles(payload);
      } catch (error) {
        console.error('[ProjectHandlers] Error adding selected files:', error);
        throw error;
      }
    });

    ipcMain.handle('projectFiles:getSelected', async (_, projectId: number) => {
      try {
        return await projectFilesService.getSelectedFiles(projectId);
      } catch (error) {
        console.error('[ProjectHandlers] Error getting selected files:', error);
        return [];
      }
    });

    ipcMain.handle('projectFiles:removeSelected', async (_, projectId: number, fileId: string) => {
      try {
        await projectFilesService.removeSelectedFile(projectId, fileId);
      } catch (error) {
        console.error('[ProjectHandlers] Error removing selected file:', error);
        throw error;
      }
    });

    ipcMain.handle('projectFiles:clearSelected', async (_, projectId: number) => {
      try {
        await projectFilesService.clearSelectedFiles(projectId);
      } catch (error) {
        console.error('[ProjectHandlers] Error clearing selected files:', error);
        throw error;
      }
    });

    console.log('[ProjectHandlers] All project IPC handlers registered');
  }
}
