@import "tailwindcss";

/* Ultra-optimized scrollbar with GPU acceleration */
@layer utilities {
  .scrollbar-ultra-smooth {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175 / 0.2) transparent;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  }

  .scrollbar-ultra-smooth::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .scrollbar-ultra-smooth::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }

  .scrollbar-ultra-smooth::-webkit-scrollbar-thumb {
    background: linear-gradient(180deg, rgb(156 163 175 / 0.2), rgb(156 163 175 / 0.3));
    border-radius: 3px;
    transition: var(--transition-ultra-fast);
    transform: translateZ(0);
    will-change: background;
  }

  .scrollbar-ultra-smooth::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(180deg, rgb(156 163 175 / 0.4), rgb(156 163 175 / 0.5));
    transform: translateZ(0) scale(1.1);
  }

  .scrollbar-ultra-smooth::-webkit-scrollbar-corner {
    background: transparent;
  }

  /* GPU acceleration utilities */
  .gpu-boost {
    transform: translateZ(0);
    will-change: transform, opacity;
    backface-visibility: hidden;
    perspective: 1000px;
  }

  .gpu-layer {
    transform: translate3d(0, 0, 0);
    will-change: transform;
    contain: layout style paint;
  }

  /* Ultra-smooth transitions */
  .transition-ultra-fast {
    transition: var(--transition-ultra-fast);
  }

  .transition-fast {
    transition: var(--transition-fast);
  }

  .transition-smooth {
    transition: var(--transition-smooth);
  }

  .transition-slow {
    transition: var(--transition-slow);
  }

  /* Performance-optimized animations */
  .animate-fade-in {
    animation: var(--animate-fade-in);
  }

  .animate-slide-in {
    animation: var(--animate-slide-in);
  }

  .animate-scale-in {
    animation: var(--animate-scale-in);
  }

  /* Tree-specific optimizations */
  .tree-container {
    contain: layout style paint;
    transform: translateZ(0);
    will-change: scroll-position;
    overflow-anchor: none;
  }

  .tree-node {
    contain: layout style;
    transform: translateZ(0);
    will-change: transform, opacity;
    isolation: isolate;
  }

  .tree-node-content {
    contain: layout;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* Hover optimizations */
  .hover-lift {
    transition: var(--transition-fast);
  }

  .hover-lift:hover {
    transform: translateZ(0) translateY(-1px);
    filter: brightness(1.05);
  }

  /* Focus optimizations */
  .focus-ring {
    outline: 2px solid transparent;
    outline-offset: 2px;
    transition: var(--transition-ultra-fast);
  }

  .focus-ring:focus-visible {
    outline-color: rgb(168 85 247 / 0.6);
    outline-offset: 0;
  }

  /* Compact checkbox styles - увеличиваем размер для лучшей читаемости */
  .checkbox-mini {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    width: 12px;
    height: 12px;
    border: 1.5px solid rgb(75 85 99);
    border-radius: 2px;
    background-color: transparent;
    cursor: pointer;
    position: relative;
    transition: var(--transition-ultra-fast);
    transform: translateZ(0);
    will-change: background-color, border-color;
    flex-shrink: 0;
  }

  .checkbox-mini:hover {
    border-color: rgb(168 85 247);
    background-color: rgb(168 85 247 / 0.05);
  }

  .checkbox-mini:checked {
    background-color: rgb(168 85 247);
    border-color: rgb(168 85 247);
  }

  .checkbox-mini:checked::after {
    content: '';
    position: absolute;
    left: 2.5px;
    top: 0.5px;
    width: 3px;
    height: 6px;
    border: solid white;
    border-width: 0 1.5px 1.5px 0;
    transform: rotate(45deg);
  }

  .checkbox-mini:indeterminate {
    background-color: rgb(192 132 252);
    border-color: rgb(192 132 252);
  }

  .checkbox-mini:indeterminate::after {
    content: '';
    position: absolute;
    left: 2px;
    top: 5px;
    width: 8px;
    height: 1.5px;
    background-color: white;
    border-radius: 1px;
  }
}

/* Base performance optimizations */
* {
  box-sizing: border-box;
}

*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.5;
  color: rgb(243 244 246);
  background-color: rgb(17 24 39);
  overflow-x: hidden;
  transform: translateZ(0);
}

/* Optimize image rendering */
img {
  max-width: 100%;
  height: auto;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Optimize button interactions */
button {
  cursor: pointer;
  user-select: none;
  touch-action: manipulation;
}

/* Optimize input interactions */
input, textarea, select {
  touch-action: manipulation;
}

/* Compact file tree styles */
.ant-tree .ant-tree-treenode {
  padding: 0 !important;
  margin: 0 !important;
}

.ant-tree .ant-tree-node-content-wrapper {
  padding: 1px 4px !important;
  line-height: 18px !important;
  height: 20px !important;
  margin: 0 !important;
}

.ant-tree .ant-tree-checkbox {
  margin-right: 4px !important;
  margin-top: 1px !important;
}

.ant-tree .ant-tree-switcher {
  width: 14px !important;
  height: 18px !important;
  line-height: 18px !important;
  margin-right: 2px !important;
}

.ant-tree .ant-tree-title {
  line-height: 18px !important;
}

.ant-tree li {
  margin: 0 !important;
  padding: 0 !important;
}

.ant-tree li .ant-tree-node-content-wrapper {
  margin: 0 !important;
}

/* Reduce motion for accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
