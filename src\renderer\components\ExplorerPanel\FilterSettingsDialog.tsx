/**
 * @file src/renderer/components/ExplorerPanel/FilterSettingsDialog.tsx
 * @description Compact dialog for configuring auto-select filters
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Chip,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  RestoreOutlined,
  TuneOutlined
} from '@mui/icons-material';
import { useAppStore } from '../../../shared/store';
import type { AutoSelectFilters } from '../../../shared/types/filters.types';
import { DEFAULT_AUTO_SELECT_FILTERS, FILTER_PRESETS } from '../../../shared/types/filters.types';

interface FilterSettingsDialogProps {
  open: boolean;
  onClose: () => void;
}

export const FilterSettingsDialog: React.FC<FilterSettingsDialogProps> = ({ open, onClose }) => {
  const { autoSelectFilters, setAutoSelectFilters } = useAppStore();
  const [localFilters, setLocalFilters] = useState<AutoSelectFilters>(autoSelectFilters);

  // Sync with store when dialog opens
  useEffect(() => {
    if (open) {
      setLocalFilters(autoSelectFilters);
    }
  }, [open, autoSelectFilters]);

  const handleSave = () => {
    setAutoSelectFilters(localFilters);
    onClose();
  };

  const handleReset = () => {
    setLocalFilters(DEFAULT_AUTO_SELECT_FILTERS);
  };

  const handlePresetApply = (presetId: string) => {
    const preset = FILTER_PRESETS.find(p => p.id === presetId);
    if (preset) {
      setLocalFilters(preset.filters);
    }
  };

  const updateFilter = (path: string, value: boolean) => {
    setLocalFilters(prev => {
      const keys = path.split('.');
      const newFilters = { ...prev };
      let current: any = newFilters;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        } else {
          current[keys[i]] = { ...current[keys[i]] };
        }
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newFilters;
    });
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          backgroundColor: '#1a1a1a',
          border: '1px solid rgba(168, 85, 247, 0.3)',
          borderRadius: 2
        }
      }}
    >
      <DialogTitle sx={{
        color: '#a855f7',
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        borderBottom: '1px solid rgba(168, 85, 247, 0.2)',
        pb: 1,
        fontSize: '1.1rem'
      }}>
        <TuneOutlined />
        Filter Settings
        <Tooltip title="Reset to defaults">
          <IconButton
            onClick={handleReset}
            sx={{ ml: 'auto', color: '#64748b' }}
          >
            <RestoreOutlined />
          </IconButton>
        </Tooltip>
      </DialogTitle>

      <DialogContent sx={{ p: 2 }}>
        {/* Quick Presets */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" sx={{ color: '#d8b4fe', mb: 1 }}>
            Quick Presets
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {FILTER_PRESETS.map(preset => (
              <Chip
                key={preset.id}
                label={preset.name}
                onClick={() => handlePresetApply(preset.id)}
                size="small"
                sx={{
                  backgroundColor: 'rgba(168, 85, 247, 0.1)',
                  color: '#c084fc',
                  border: '1px solid rgba(168, 85, 247, 0.3)',
                  '&:hover': {
                    backgroundColor: 'rgba(168, 85, 247, 0.2)'
                  }
                }}
              />
            ))}
          </Box>
        </Box>

        <Divider sx={{ borderColor: 'rgba(168, 85, 247, 0.2)', mb: 2 }} />

        {/* File Types */}
        <Typography variant="subtitle2" sx={{ color: '#d8b4fe', mb: 1 }}>
          File Types
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1, mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.images.enabled}
                onChange={(e) => updateFilter('fileTypes.images.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Images <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    ({localFilters.fileTypes.images.extensions.join(', ')})
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.vectors.enabled}
                onChange={(e) => updateFilter('fileTypes.vectors.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  SVG <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    ({localFilters.fileTypes.vectors.extensions.join(', ')})
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.logs.enabled}
                onChange={(e) => updateFilter('fileTypes.logs.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Logs <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    ({localFilters.fileTypes.logs.extensions.join(', ')})
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.lockFiles.enabled}
                onChange={(e) => updateFilter('fileTypes.lockFiles.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Lock Files <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (package-lock.json, yarn.lock, etc.)
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.binary.enabled}
                onChange={(e) => updateFilter('fileTypes.binary.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Binary <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    ({localFilters.fileTypes.binary.extensions.join(', ')})
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.icons.enabled}
                onChange={(e) => updateFilter('fileTypes.icons.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Icons <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (**/icons/**, favicon.*, etc.)
                  </Typography>
                </Typography>
              </Box>
            }
          />
        </Box>

        <Divider sx={{ borderColor: 'rgba(168, 85, 247, 0.2)', mb: 2 }} />

        {/* Directories */}
        <Typography variant="subtitle2" sx={{ color: '#d8b4fe', mb: 1 }}>
          Directories
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1, mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.directories.nodeModules.enabled}
                onChange={(e) => updateFilter('directories.nodeModules.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  node_modules <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (node_modules/)
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.directories.buildOutputs.enabled}
                onChange={(e) => updateFilter('directories.buildOutputs.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Build Outputs <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (dist/, build/, out/)
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.directories.versionControl.enabled}
                onChange={(e) => updateFilter('directories.versionControl.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Version Control <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (.git/, .svn/, .hg/)
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.directories.ideFiles.enabled}
                onChange={(e) => updateFilter('directories.ideFiles.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  IDE Files <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (.vscode/, .idea/, __pycache__/)
                  </Typography>
                </Typography>
              </Box>
            }
          />
        </Box>

        <Divider sx={{ borderColor: 'rgba(168, 85, 247, 0.2)', mb: 2 }} />

        {/* Other Settings */}
        <Typography variant="subtitle2" sx={{ color: '#d8b4fe', mb: 1 }}>
          Other Settings
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1, mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.hiddenFiles.enabled}
                onChange={(e) => updateFilter('hiddenFiles.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Hidden Files <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (.*files)
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.gitignore?.enabled ?? false}
                onChange={(e) => updateFilter('gitignore.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Use .gitignore <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (auto-load patterns)
                  </Typography>
                </Typography>
              </Box>
            }
          />
        </Box>

        <Typography variant="caption" sx={{ color: '#64748b', display: 'block', textAlign: 'center' }}>
          Filters only affect Auto Select. Manual selection always available.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ p: 2, borderTop: '1px solid rgba(168, 85, 247, 0.2)' }}>
        <Button 
          onClick={onClose}
          sx={{ color: '#64748b' }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: '#a855f7',
            '&:hover': { backgroundColor: '#9333ea' }
          }}
        >
          Save Settings
        </Button>
      </DialogActions>
    </Dialog>
  );
};
