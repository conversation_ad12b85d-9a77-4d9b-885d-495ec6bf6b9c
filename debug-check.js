/**
 * Диагностический скрипт для проверки конфигурации
 */

const fs = require('fs');
const path = require('path');

console.log('=== ДИАГНОСТИКА КОНФИГУРАЦИИ ===\n');

// Проверяем структуру проекта
console.log('📁 Структура проекта:');
const checkPath = (filePath, description) => {
  const exists = fs.existsSync(filePath);
  console.log(`${exists ? '✅' : '❌'} ${description}: ${filePath}`);
  return exists;
};

checkPath('./package.json', 'package.json');
checkPath('./index.html', 'index.html');
checkPath('./vite.config.ts', 'vite.config.ts');
checkPath('./src/main/main.ts', 'main.ts');
checkPath('./src/renderer/main.tsx', 'main.tsx');
checkPath('./src/renderer/TestApp.tsx', 'TestApp.tsx');
checkPath('./src/renderer/preload.ts', 'preload.ts');
checkPath('./dist-electron', 'dist-electron (сборка)');

console.log('\n📋 Содержимое package.json scripts:');
try {
  const pkg = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
  console.log('Scripts:', pkg.scripts);
  console.log('Main:', pkg.main);
} catch (e) {
  console.log('❌ Ошибка чтения package.json:', e.message);
}

console.log('\n🔧 Содержимое index.html:');
try {
  const indexHtml = fs.readFileSync('./index.html', 'utf8');
  console.log(indexHtml);
} catch (e) {
  console.log('❌ Ошибка чтения index.html:', e.message);
}

console.log('\n🌐 Переменные окружения:');
console.log('VITE_DEV_SERVER_URL:', process.env.VITE_DEV_SERVER_URL);
console.log('NODE_ENV:', process.env.NODE_ENV);

console.log('\n=== ДИАГНОСТИКА ЗАВЕРШЕНА ===');
