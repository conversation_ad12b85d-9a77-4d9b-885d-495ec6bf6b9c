/**
 * @file src/renderer/components/SettingsPanel.tsx
 * @description Settings panel component for application configuration
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Card,
  CardContent
} from '@mui/material';
import { RestartAlt, Warning } from '@mui/icons-material';

export const SettingsPanel: React.FC = () => {
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  const handleFactoryReset = async () => {
    setIsResetting(true);

    try {
      // Очищаем localStorage
      localStorage.clear();
      console.log('Cleared localStorage');

      // Очищаем данные Electron через IPC
      if (window.electronAPI?.clearAppData) {
        await window.electronAPI.clearAppData();
        console.log('Cleared Electron app data');
      }

      // Показываем уведомление и перезагружаем приложение
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('Error during factory reset:', error);
    } finally {
      setIsResetting(false);
      setResetDialogOpen(false);
    }
  };

  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 3 }}>
        {/* Раздел сброса данных */}
        <Card sx={{
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: 2
        }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Warning sx={{ color: '#ef4444' }} />
              <Typography variant="h6" sx={{ color: '#ef4444', fontWeight: 600 }}>
                Danger Zone
              </Typography>
            </Box>

            <Typography variant="body2" sx={{ color: '#94a3b8', mb: 3 }}>
              Reset all application data to factory defaults. This will remove all projects,
              tabs, settings, and cached data. This action cannot be undone.
            </Typography>

            <Button
              variant="outlined"
              color="error"
              startIcon={<RestartAlt />}
              onClick={() => setResetDialogOpen(true)}
              sx={{
                borderColor: '#ef4444',
                color: '#ef4444',
                '&:hover': {
                  borderColor: '#dc2626',
                  backgroundColor: 'rgba(239, 68, 68, 0.1)'
                }
              }}
            >
              Factory Reset
            </Button>
          </CardContent>
        </Card>

        {/* Placeholder для других настроек */}
        <Card sx={{
          backgroundColor: 'rgba(147, 51, 234, 0.1)',
          border: '1px solid rgba(147, 51, 234, 0.3)',
          borderRadius: 2
        }}>
          <CardContent>
            <Typography variant="h6" sx={{ color: '#d8b4fe', fontWeight: 600, mb: 2 }}>
              General Settings
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b' }}>
              Additional application settings will be implemented here
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* Диалог подтверждения сброса */}
      <Dialog
        open={resetDialogOpen}
        onClose={() => setResetDialogOpen(false)}
        sx={{
          '& .MuiDialog-paper': {
            backgroundColor: '#1a1a1a',
            border: '1px solid rgba(239, 68, 68, 0.3)'
          }
        }}
      >
        <DialogTitle sx={{ color: '#ef4444', display: 'flex', alignItems: 'center', gap: 1 }}>
          <Warning />
          Factory Reset Confirmation
        </DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            This action will permanently delete all application data!
          </Alert>
          <Typography sx={{ color: '#94a3b8' }}>
            Are you sure you want to reset SmartRAG to factory defaults? This will:
          </Typography>
          <Box component="ul" sx={{ color: '#94a3b8', mt: 1, pl: 2 }}>
            <li>Remove all project tabs and data</li>
            <li>Clear all cached files and indexes</li>
            <li>Reset all application settings</li>
            <li>Clear localStorage and Electron data</li>
          </Box>
          <Typography sx={{ color: '#ef4444', mt: 2, fontWeight: 600 }}>
            This action cannot be undone!
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setResetDialogOpen(false)}
            sx={{ color: '#94a3b8' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleFactoryReset}
            color="error"
            variant="contained"
            disabled={isResetting}
            startIcon={<RestartAlt />}
          >
            {isResetting ? 'Resetting...' : 'Reset to Factory Defaults'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};
