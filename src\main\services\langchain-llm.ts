/**
 * LangChain-integrated LLM Service
 * Provides embeddings and chat completions using Lang<PERSON>hain abstractions
 */

import { Embeddings } from '@langchain/core/embeddings';
import { BaseLLM } from '@langchain/core/language_models/llms';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { RunnableSequence } from '@langchain/core/runnables';
import { Document } from '@langchain/core/documents';

// Custom Ollama Embeddings implementation
class OllamaEmbeddings extends Embeddings {
  private model: string;
  private baseUrl: string;

  constructor(options: { model?: string; baseUrl?: string } = {}) {
    super({});
    this.model = options.model || 'nomic-embed-text';
    this.baseUrl = options.baseUrl || 'http://localhost:11434';
  }

  async embedDocuments(texts: string[]): Promise<number[][]> {
    const embeddings: number[][] = [];
    
    for (const text of texts) {
      try {
        const response = await fetch(`${this.baseUrl}/api/embeddings`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: this.model,
            prompt: text,
          }),
        });

        if (!response.ok) {
          throw new Error(`Ollama API error: ${response.statusText}`);
        }

        const data = await response.json();
        embeddings.push(data.embedding);
      } catch (error) {
        console.warn(`⚠️ Failed to generate embedding for text, using dummy:`, error);
        // Fallback to dummy embedding
        embeddings.push(Array(768).fill(0).map(() => Math.random()));
      }
    }

    return embeddings;
  }

  async embedQuery(text: string): Promise<number[]> {
    const embeddings = await this.embedDocuments([text]);
    return embeddings[0];
  }
}

// Custom Ollama LLM implementation
class OllamaLLM extends BaseLLM {
  private model: string;
  private baseUrl: string;

  constructor(options: { model?: string; baseUrl?: string } = {}) {
    super({});
    this.model = options.model || 'llama3:8b';
    this.baseUrl = options.baseUrl || 'http://localhost:11434';
  }

  _llmType(): string {
    return 'ollama';
  }

  async _generate(prompts: string[]): Promise<any> {
    const results = [];
    for (const prompt of prompts) {
      const response = await this._call(prompt);
      results.push({
        text: response,
        generationInfo: {},
      });
    }
    return {
      generations: results.map(r => [r]),
      llmOutput: {},
    };
  }

  async _call(prompt: string): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.model,
          prompt: prompt,
          stream: false,
        }),
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.statusText}`);
      }

      const data = await response.json();
      return data.response;
    } catch (error) {
      console.warn(`⚠️ Failed to generate response, using dummy:`, error);
      return `This is a dummy response for the prompt: "${prompt.substring(0, 100)}..."`;
    }
  }
}

export type LLMProvider = 'ollama' | 'gemini';

export interface LLMConfig {
  provider: LLMProvider;
  apiKey?: string;
  model: string;
  embeddingModel?: string;
  baseUrl?: string;
}

export class LangChainLLMService {
  private config: LLMConfig;
  private _embeddings!: Embeddings;
  private llm!: BaseLLM;
  private ragChain: RunnableSequence | null = null;

  constructor(config: LLMConfig) {
    this.config = config;
    this.initializeModels();
  }

  get embeddings(): Embeddings {
    return this._embeddings;
  }

  private initializeModels(): void {
    switch (this.config.provider) {
      case 'ollama':
        this._embeddings = new OllamaEmbeddings({
          model: this.config.embeddingModel || 'nomic-embed-text',
          baseUrl: this.config.baseUrl,
        });
        this.llm = new OllamaLLM({
          model: this.config.model,
          baseUrl: this.config.baseUrl,
        });
        break;
      
      case 'gemini':
        // TODO: Implement Gemini integration using @langchain/google-genai
        throw new Error('Gemini integration not yet implemented');
      
      default:
        throw new Error(`Unsupported LLM provider: ${this.config.provider}`);
    }

    this.initializeRAGChain();
  }

  private initializeRAGChain(): void {
    // Create a RAG chain using LangChain LCEL (LangChain Expression Language)
    const prompt = ChatPromptTemplate.fromTemplate(`
You are a helpful coding assistant. Use the following context to answer the question.

Context:
{context}

Question: {question}

Answer:`);

    this.ragChain = RunnableSequence.from([
      {
        context: (input: { context: string; question: string }) => input.context,
        question: (input: { context: string; question: string }) => input.question,
      },
      prompt,
      this.llm,
      new StringOutputParser(),
    ]);
  }

  /**
   * Generate embeddings for a single text
   */
  async generateEmbedding(text: string): Promise<number[]> {
    return await this.embeddings.embedQuery(text);
  }

  /**
   * Generate embeddings for multiple texts
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    return await this.embeddings.embedDocuments(texts);
  }

  /**
   * Generate embeddings for LangChain Documents
   */
  async embedDocuments(documents: Document[]): Promise<{ document: Document; embedding: number[] }[]> {
    const texts = documents.map(doc => doc.pageContent);
    const embeddings = await this.generateEmbeddings(texts);
    
    return documents.map((document, index) => ({
      document,
      embedding: embeddings[index],
    }));
  }

  /**
   * Generate a response using the LLM
   */
  async generateResponse(prompt: string): Promise<string> {
    return await this.llm.invoke(prompt);
  }

  /**
   * Generate a RAG response using context and question
   */
  async generateRAGResponse(context: string, question: string): Promise<string> {
    if (!this.ragChain) {
      throw new Error('RAG chain not initialized');
    }

    return await this.ragChain.invoke({ context, question });
  }

  /**
   * Generate RAG response with retrieved documents
   */
  async generateRAGResponseWithDocs(
    retrievedDocs: Document[],
    question: string
  ): Promise<{ answer: string; sources: Document[] }> {
    const context = retrievedDocs
      .map(doc => `File: ${doc.metadata.file}\n${doc.pageContent}`)
      .join('\n\n---\n\n');

    const answer = await this.generateRAGResponse(context, question);

    return {
      answer,
      sources: retrievedDocs,
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<LLMConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.initializeModels();
  }

  /**
   * Get current configuration
   */
  getConfig(): LLMConfig {
    return { ...this.config };
  }

  /**
   * Test connection to the LLM service
   */
  async testConnection(): Promise<boolean> {
    try {
      const testResponse = await this.generateResponse('Hello, this is a test.');
      return testResponse.length > 0;
    } catch (error) {
      console.error('LLM connection test failed:', error);
      return false;
    }
  }

  /**
   * Test embedding generation
   */
  async testEmbedding(): Promise<boolean> {
    try {
      const embedding = await this.generateEmbedding('Test text for embedding');
      return Array.isArray(embedding) && embedding.length > 0;
    } catch (error) {
      console.error('Embedding test failed:', error);
      return false;
    }
  }
}

// Default instance
export const langChainLLMService = new LangChainLLMService({
  provider: 'ollama',
  model: 'llama3:8b',
  embeddingModel: 'nomic-embed-text',
  baseUrl: 'http://localhost:11434',
});
