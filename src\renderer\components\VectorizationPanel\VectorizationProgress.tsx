/**
 * @file src/renderer/components/VectorizationPanel/VectorizationProgress.tsx
 * @description Progress tracking component for vectorization process
 */

import React from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  Chip,
  Paper,
  Collapse
} from '@mui/material';
import { formatDistanceToNow, format } from 'date-fns';

interface VectorizationProgressItem {
  id: string;
  file: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  chunks: number;
  progress: number;
  error?: string;
  startTime?: number;
  endTime?: number;
}

interface VectorizationProgressProps {
  isActive: boolean;
  currentFile?: string;
  overallProgress: number;
  completedFiles: number;
  totalFiles: number;
  items: VectorizationProgressItem[];
  startTime?: number;
  showDetails?: boolean;
}

export const VectorizationProgress: React.FC<VectorizationProgressProps> = ({
  isActive,
  currentFile,
  overallProgress,
  completedFiles,
  totalFiles,
  items,
  startTime,
  showDetails = true
}) => {
  // Calculate elapsed time if startTime is available
  const elapsedTime = startTime ? Date.now() - startTime : null;

  // Calculate estimated remaining time based on progress
  const estimatedRemainingTime = elapsedTime && overallProgress > 0
    ? (elapsedTime / overallProgress) * (100 - overallProgress)
    : null;

  if (!isActive && items.length === 0) {
    return null;
  }

  const getStatusColor = (status: VectorizationProgressItem['status']) => {
    switch (status) {
      case 'pending': return '#64748b';
      case 'processing': return '#9333ea';
      case 'completed': return '#22c55e';
      case 'error': return '#ef4444';
      default: return '#64748b';
    }
  };

  const getStatusLabel = (status: VectorizationProgressItem['status']) => {
    switch (status) {
      case 'pending': return 'PENDING';
      case 'processing': return 'PROCESSING';
      case 'completed': return 'COMPLETED';
      case 'error': return 'ERROR';
      default: return 'UNKNOWN';
    }
  };

  return (
    <Paper
      sx={{
        p: 3,
        backgroundColor: 'rgba(147, 51, 234, 0.05)',
        border: '1px solid rgba(147, 51, 234, 0.2)',
        borderRadius: 2
      }}
    >
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ color: '#d8b4fe', fontWeight: 600 }}>
          {isActive ? 'Vectorization in Progress' : 'Vectorization Complete'}
        </Typography>
        <Typography variant="caption" sx={{ color: '#9ca3af' }}>
          {completedFiles} / {totalFiles} files
        </Typography>
      </Box>

      {/* Overall Progress */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" sx={{ color: '#c084fc', fontWeight: 500 }}>
            Overall Progress
          </Typography>
          <Typography variant="caption" sx={{ color: '#9ca3af' }}>
            {Math.round(overallProgress)}%
          </Typography>
        </Box>
        
        <LinearProgress
          variant="determinate"
          value={overallProgress}
          sx={{
            height: 8,
            borderRadius: 4,
            backgroundColor: 'rgba(147, 51, 234, 0.2)',
            '& .MuiLinearProgress-bar': {
              background: 'linear-gradient(90deg, #9333ea, #a855f7)',
              borderRadius: 4,
            }
          }}
        />

        {currentFile && (
          <Typography 
            variant="caption" 
            sx={{ 
              color: '#9ca3af', 
              mt: 1, 
              display: 'block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            Current: {currentFile}
          </Typography>
        )}
      </Box>

      {/* Timing Information */}
      {startTime && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="caption" sx={{ color: '#9ca3af' }}>
            Started: {format(new Date(startTime), 'HH:mm:ss')}
          </Typography>
          {elapsedTime && (
            <Typography variant="caption" sx={{ color: '#9ca3af' }}>
              Elapsed: {formatDistanceToNow(new Date(Date.now() - elapsedTime), { 
                includeSeconds: true, 
                addSuffix: false 
              })}
            </Typography>
          )}
          {estimatedRemainingTime && isActive && (
            <Typography variant="caption" sx={{ color: '#9ca3af' }}>
              ETA: {formatDistanceToNow(new Date(Date.now() + estimatedRemainingTime), { 
                includeSeconds: true, 
                addSuffix: true 
              })}
            </Typography>
          )}
        </Box>
      )}

      {/* Detailed Progress List */}
      <Collapse in={showDetails && items.length > 0}>
        <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
          <Typography variant="subtitle2" sx={{ color: '#c084fc', mb: 1, fontWeight: 500 }}>
            File Progress
          </Typography>
          
          <List sx={{ p: 0 }}>
            {items.map((item) => (
              <ListItem
                key={item.id}
                sx={{
                  py: 1,
                  px: 0,
                  borderBottom: '1px solid rgba(147, 51, 234, 0.1)',
                  '&:last-child': { borderBottom: 'none' }
                }}
              >
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          color: '#e2e8f0',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          flex: 1
                        }}
                      >
                        {item.file}
                      </Typography>
                      
                      <Chip
                        label={getStatusLabel(item.status)}
                        size="small"
                        sx={{
                          backgroundColor: `${getStatusColor(item.status)}20`,
                          color: getStatusColor(item.status),
                          fontWeight: 600,
                          fontSize: '0.7rem',
                          minWidth: 80
                        }}
                      />
                    </Box>
                  }
                  secondary={
                    <Box sx={{ mt: 0.5 }}>
                      {item.status === 'processing' && (
                        <LinearProgress
                          variant="determinate"
                          value={item.progress}
                          sx={{
                            height: 4,
                            borderRadius: 2,
                            backgroundColor: 'rgba(147, 51, 234, 0.1)',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: '#9333ea',
                              borderRadius: 2,
                            }
                          }}
                        />
                      )}
                      
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                        <Typography variant="caption" sx={{ color: '#64748b' }}>
                          {item.chunks} chunks
                        </Typography>
                        
                        {item.status === 'processing' && (
                          <Typography variant="caption" sx={{ color: '#64748b' }}>
                            {Math.round(item.progress)}%
                          </Typography>
                        )}
                        
                        {item.status === 'completed' && item.endTime && item.startTime && (
                          <Typography variant="caption" sx={{ color: '#64748b' }}>
                            {((item.endTime - item.startTime) / 1000).toFixed(1)}s
                          </Typography>
                        )}
                        
                        {item.status === 'error' && item.error && (
                          <Typography variant="caption" sx={{ color: '#ef4444' }}>
                            {item.error}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Box>
      </Collapse>
    </Paper>
  );
};
