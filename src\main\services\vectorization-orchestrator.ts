/**
 * @file src/main/services/vectorization-orchestrator.ts
 * @description Orchestrator service for coordinating vectorization processes
 */

import { EventEmitter } from 'events';
import { langChainLLMService } from './langchain-llm';
import { LangChainLanceDBVectorStore } from './langchain-vectorstore';
import { sqliteService } from './sqlite';
import { Document } from '@langchain/core/documents';
import path from 'path';
import fs from 'fs/promises';
import type {
  VectorizationConfig,
  VectorizationStats,
  FileVectorizationInfo,
  VectorizationUpdatePayload,
  ChunkData
} from '../../shared/types/vectorization.types';



// Using VectorizationUpdatePayload from shared types

class VectorizationOrchestrator extends EventEmitter {
  private activeVectorizations = new Map<number, boolean>();
  private vectorStores = new Map<number, LangChainLanceDBVectorStore>();
  private chunksCache = new Map<number, { chunks: any[], timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Start vectorization process for a project
   */
  async startVectorization(
    projectId: number,
    config: VectorizationConfig
  ): Promise<void> {
    console.log(`[VectorizationOrchestrator] Starting vectorization for project ${projectId} with provider: ${config.provider}`);

    if (this.activeVectorizations.get(projectId)) {
      throw new Error(`Vectorization already in progress for project ${projectId}`);
    }

    this.activeVectorizations.set(projectId, true);

    // Clear cache to ensure fresh data
    this.clearChunksCache(projectId);

    try {
      // Update LLM service configuration
      langChainLLMService.updateConfig({
        provider: config.provider,
        model: config.model,
        embeddingModel: config.embeddingModel,
        baseUrl: config.baseUrl,
        apiKey: config.apiKey
      });

      // Get chunks that need vectorization
      const chunks = await this.getUnvectorizedChunks(projectId);
      
      if (chunks.length === 0) {
        this.emit('vectorization_update', {
          type: 'batch_complete',
          projectId,
          stats: await this.getVectorizationStats(projectId)
        } as VectorizationUpdatePayload);
        return;
      }

      const totalFiles = new Set(chunks.map(chunk => chunk.metadata.file)).size;
      let completedFiles = 0;
      let processedChunks = 0;

      // Get or create vector store
      const vectorStore = await this.getVectorStore(projectId);

      // Process chunks in batches
      const batchSize = config.batchSize || 10;
      for (let i = 0; i < chunks.length; i += batchSize) {
        if (!this.activeVectorizations.get(projectId)) {
          break; // Vectorization was cancelled
        }

        const batch = chunks.slice(i, i + batchSize);
        const currentFile = batch[0]?.metadata.file;

        this.emit('vectorization_update', {
          type: 'progress',
          projectId,
          file: currentFile,
          progress: (processedChunks / chunks.length) * 100,
          completedFiles,
          totalFiles
        } as VectorizationUpdatePayload);

        try {
          // Add batch to vector store
          await vectorStore.addDocuments(batch);
          processedChunks += batch.length;

          // Check if file is complete
          const fileChunks = chunks.filter(c => c.metadata.file === currentFile);
          const processedFileChunks = processedChunks >= chunks.findIndex(c => c.metadata.file !== currentFile);
          
          if (processedFileChunks) {
            completedFiles++;
            this.emit('vectorization_update', {
              type: 'file_complete',
              projectId,
              file: currentFile,
              completedFiles,
              totalFiles
            } as VectorizationUpdatePayload);
          }

        } catch (error) {
          console.error(`Error vectorizing batch:`, error);
          this.emit('vectorization_update', {
            type: 'error',
            projectId,
            file: currentFile,
            error: (error as Error).message
          } as VectorizationUpdatePayload);
        }
      }

      // Complete vectorization
      const finalStats = await this.getVectorizationStats(projectId);
      this.emit('vectorization_update', {
        type: 'batch_complete',
        projectId,
        stats: finalStats
      } as VectorizationUpdatePayload);

    } catch (error) {
      console.error(`Vectorization error for project ${projectId}:`, error);
      this.emit('vectorization_update', {
        type: 'error',
        projectId,
        error: (error as Error).message
      } as VectorizationUpdatePayload);
    } finally {
      this.activeVectorizations.set(projectId, false);
    }
  }

  /**
   * Stop vectorization process for a project
   */
  async stopVectorization(projectId: number): Promise<void> {
    this.activeVectorizations.set(projectId, false);
  }

  /**
   * Get vectorization statistics for a project
   */
  async getVectorizationStats(projectId: number): Promise<VectorizationStats> {
    const vectorStore = await this.getVectorStore(projectId);
    const chunks = await this.getAllChunks(projectId);
    const vectorStoreStats = await vectorStore.getStats();
    const vectorizedChunks = vectorStoreStats.totalDocuments;

    const files = new Set(chunks.map(chunk => chunk.metadata.file));
    const vectorizedFiles = new Set();

    // Count vectorized files (files with all chunks vectorized)
    for (const file of files) {
      const fileChunks = chunks.filter(c => c.metadata.file === file);
      const fileVectorizedChunks = await vectorStore.searchByMetadata({ file });
      
      if (fileVectorizedChunks.length === fileChunks.length) {
        vectorizedFiles.add(file);
      }
    }

    return {
      totalChunks: chunks.length,
      vectorizedChunks,
      totalFiles: files.size,
      vectorizedFiles: vectorizedFiles.size,
      totalVectors: vectorizedChunks,
      embeddingDimensions: 768, // TODO: Get from model config
      indexingTime: 0, // TODO: Track actual time
      averageTimePerChunk: 0, // TODO: Calculate from tracking
      provider: langChainLLMService.getConfig().provider || 'ollama',
      model: langChainLLMService.getConfig().embeddingModel || 'nomic-embed-text',
      lastUpdated: Date.now()
    };
  }

  /**
   * Get file vectorization information
   */
  async getFileVectorizationInfo(projectId: number): Promise<FileVectorizationInfo[]> {
    const chunks = await this.getAllChunks(projectId);
    const vectorStore = await this.getVectorStore(projectId);
    
    const fileMap = new Map<string, FileVectorizationInfo>();

    // Initialize file info
    for (const chunk of chunks) {
      const file = chunk.metadata.file;
      if (!fileMap.has(file)) {
        fileMap.set(file, {
          file,
          chunks: 0,
          vectorizedChunks: 0,
          vectors: 0,
          status: 'pending'
        });
      }
      fileMap.get(file)!.chunks++;
    }

    // Count vectorized chunks per file
    for (const [file, info] of fileMap) {
      const vectorizedChunks = await vectorStore.searchByMetadata({ file });
      info.vectorizedChunks = vectorizedChunks.length;
      info.vectors = vectorizedChunks.length;

      // Determine status
      if (info.vectorizedChunks === 0) {
        info.status = 'pending';
      } else if (info.vectorizedChunks < info.chunks) {
        info.status = 'partial';
      } else {
        info.status = 'complete';
      }
    }

    return Array.from(fileMap.values());
  }

  /**
   * Clear all vectors for a project
   */
  async clearVectors(projectId: number): Promise<void> {
    const vectorStore = await this.getVectorStore(projectId);
    await vectorStore.clearAll();
    
    // Remove from cache
    this.vectorStores.delete(projectId);
  }

  /**
   * Test vector search functionality
   */
  async testVectorSearch(projectId: number, query: string, k: number = 5): Promise<any[]> {
    const vectorStore = await this.getVectorStore(projectId);
    return await vectorStore.similaritySearch(query, k);
  }

  /**
   * Get or create vector store for project
   */
  private async getVectorStore(projectId: number): Promise<LangChainLanceDBVectorStore> {
    if (this.vectorStores.has(projectId)) {
      return this.vectorStores.get(projectId)!;
    }

    const embeddings = langChainLLMService.embeddings;
    const vectorStore = new LangChainLanceDBVectorStore(embeddings, { projectId });
    await vectorStore.initialize();
    
    this.vectorStores.set(projectId, vectorStore);
    return vectorStore;
  }

  /**
   * Get chunks that haven't been vectorized yet
   */
  private async getUnvectorizedChunks(projectId: number): Promise<any[]> {
    try {
      const allChunks = await this.getAllChunks(projectId);
      const vectorStore = await this.getVectorStore(projectId);

      // Get list of vectorized chunk IDs
      const vectorizedChunkIds = new Set<string>();

      // Query vector store to get all existing document IDs
      try {
        // Use empty search to get all documents, then filter by metadata
        const existingDocs = await vectorStore.similaritySearch('', 10000, { projectId });
        existingDocs.forEach(doc => {
          if (doc.metadata.chunkId) {
            vectorizedChunkIds.add(doc.metadata.chunkId);
          }
        });
      } catch (error) {
        console.warn('[VectorizationOrchestrator] Could not get existing vectors:', error);
      }

      // Filter out chunks that are already vectorized
      const unvectorizedChunks = allChunks.filter(chunk =>
        !vectorizedChunkIds.has(chunk.id)
      );

      console.log(`[VectorizationOrchestrator] Found ${unvectorizedChunks.length} unvectorized chunks out of ${allChunks.length} total`);
      return unvectorizedChunks;
    } catch (error) {
      console.error('[VectorizationOrchestrator] Error getting unvectorized chunks:', error);
      return [];
    }
  }

  /**
   * Get all chunks for a project (with caching)
   */
  private async getAllChunks(projectId: number): Promise<any[]> {
    try {
      // Check cache first
      const cached = this.chunksCache.get(projectId);
      if (cached && (Date.now() - cached.timestamp) < this.CACHE_TTL) {
        console.log(`[VectorizationOrchestrator] Using cached chunks for project ${projectId}`);
        return cached.chunks;
      }
      const project = sqliteService.getProjectById(projectId);
      if (!project) {
        throw new Error(`Project ${projectId} not found`);
      }

      const chunksDirPath = path.join(project.dataPath, 'chunks');

      // Check if chunks directory exists
      if (!await fs.access(chunksDirPath).then(() => true).catch(() => false)) {
        console.log(`[VectorizationOrchestrator] No chunks directory found for project ${projectId}`);
        return [];
      }

      const allChunks: any[] = [];
      const chunkFiles = await fs.readdir(chunksDirPath);

      for (const file of chunkFiles) {
        if (file.endsWith('.json')) {
          try {
            const filePath = path.join(chunksDirPath, file);
            const fileContent = await fs.readFile(filePath, 'utf-8');
            const chunks = JSON.parse(fileContent);

            if (Array.isArray(chunks)) {
              allChunks.push(...chunks);
            }
          } catch (error) {
            console.warn(`[VectorizationOrchestrator] Could not read chunk file ${file}:`, error);
          }
        }
      }

      console.log(`[VectorizationOrchestrator] Loaded ${allChunks.length} chunks from ${chunkFiles.length} files`);

      // Cache the results
      this.chunksCache.set(projectId, {
        chunks: allChunks,
        timestamp: Date.now()
      });

      return allChunks;
    } catch (error) {
      console.error('[VectorizationOrchestrator] Error getting all chunks:', error);
      return [];
    }
  }

  /**
   * Clear chunks cache for a project (call when chunks are modified)
   */
  private clearChunksCache(projectId: number): void {
    this.chunksCache.delete(projectId);
    console.log(`[VectorizationOrchestrator] Cleared chunks cache for project ${projectId}`);
  }

  /**
   * Clear all caches
   */
  private clearAllCaches(): void {
    this.chunksCache.clear();
    console.log('[VectorizationOrchestrator] Cleared all caches');
  }
}

// Export singleton instance
export const vectorizationOrchestrator = new VectorizationOrchestrator();
