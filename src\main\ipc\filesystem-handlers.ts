/**
 * @file src/main/ipc/filesystem-handlers.ts
 * @description IPC handlers for filesystem operations and vectorization.
 * Handles directory reading, file operations, and vectorization management.
 */

import { ipcMain, dialog } from 'electron';
import fs from 'fs/promises';
import path from 'path';
import { getTokenCount } from '../services/langchain-splitter';
import { sqliteService } from '../services/sqlite';
import { WindowManager } from '../managers/window-manager';
import { ollamaAPIService } from '../services/ollama-api';

export class FilesystemHandlers {
  private windowManager: WindowManager;

  constructor(windowManager: WindowManager) {
    this.windowManager = windowManager;
    this.registerHandlers();
    this.registerOllamaHandlers();
  }

  private registerHandlers(): void {
    // Dialog handlers
    ipcMain.handle('dialog:openDirectory', async () => {
      console.log('[FilesystemHandlers] Opening directory dialog...');
      try {
        const mainWindow = this.windowManager.getMainWindow();
        if (!mainWindow) {
          throw new Error('Main window not available');
        }

        const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
          properties: ['openDirectory']
        });
        console.log('[FilesystemHandlers] Dialog result:', { canceled, filePaths });
        if (canceled) return null;
        return filePaths[0];
      } catch (error) {
        console.error('[FilesystemHandlers] Error opening directory dialog:', error);
        return null;
      }
    });

    // File system handlers
    ipcMain.handle('fs:readDirectory', async (_, dirPath: string) => {
      try {
        return await this.readDirectory(dirPath);
      } catch (error) {
        console.error(`Failed to read directory ${dirPath}:`, error);
        return null;
      }
    });

    ipcMain.handle('fs:readDirectoryLazy', async (_, dirPath: string) => {
      try {
        return await this.readDirectoryLazy(dirPath);
      } catch (error) {
        console.error(`Failed to read directory lazily ${dirPath}:`, error);
        return null;
      }
    });

    ipcMain.handle('fs:readFile', async (_, filePath: string) => {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        return content;
      } catch (error) {
        console.error(`Failed to read file ${filePath}:`, error);
        throw error;
      }
    });

    ipcMain.handle('fs:getTokenCount', async (_, filePath: string) => {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        return getTokenCount(content);
      } catch (error) {
        // For binary files or files that can't be read, return 0
        return 0;
      }
    });

    // Vectorization handlers
    this.registerVectorizationHandlers();

    // App data management
    ipcMain.handle('app:clearData', async () => {
      try {
        const { app } = require('electron');
        const fsSync = require('fs').promises;
        const pathModule = require('path');

        // Получаем пути к данным приложения
        const userDataPath = app.getPath('userData');
        const appDataPath = app.getPath('appData');

        console.log('[FilesystemHandlers] Clearing app data...');
        console.log('[FilesystemHandlers] UserData path:', userDataPath);

        // Очищаем базу данных SQLite
        sqliteService.clearAllData();

        // Очищаем кеш и временные файлы
        const cachePath = pathModule.join(userDataPath, 'cache');
        const tempPath = pathModule.join(userDataPath, 'temp');

        try {
          await fsSync.rmdir(cachePath, { recursive: true });
          console.log('[FilesystemHandlers] Cleared cache directory');
        } catch (error) {
          console.log('[FilesystemHandlers] Cache directory not found or already cleared');
        }

        try {
          await fsSync.rmdir(tempPath, { recursive: true });
          console.log('[FilesystemHandlers] Cleared temp directory');
        } catch (error) {
          console.log('[FilesystemHandlers] Temp directory not found or already cleared');
        }

        console.log('[FilesystemHandlers] App data cleared successfully');
      } catch (error) {
        console.error('[FilesystemHandlers] Error clearing app data:', error);
        throw error;
      }
    });

    console.log('[FilesystemHandlers] All filesystem IPC handlers registered');
  }

  private registerVectorizationHandlers(): void {
    // Vectorization handlers
    ipcMain.handle('vectorization:start', async (_, { projectId, config }: {
      projectId: number;
      config: any
    }) => {
      console.log('[FilesystemHandlers] Starting vectorization for project', projectId, 'with config:', config);

      try {
        const { vectorizationOrchestrator } = await import('../services/vectorization-orchestrator');
        await vectorizationOrchestrator.startVectorization(projectId, config);
        return { success: true, message: 'Vectorization started successfully.' };
      } catch (error) {
        console.error('[FilesystemHandlers] Error starting vectorization:', error);
        return { success: false, message: `Error starting vectorization: ${(error as Error).message}` };
      }
    });

    ipcMain.handle('vectorization:stop', async (_, projectId: number) => {
      console.log('[FilesystemHandlers] Stopping vectorization for project', projectId);

      try {
        const { vectorizationOrchestrator } = await import('../services/vectorization-orchestrator');
        await vectorizationOrchestrator.stopVectorization(projectId);
        return { success: true, message: 'Vectorization stopped successfully.' };
      } catch (error) {
        console.error('[FilesystemHandlers] Error stopping vectorization:', error);
        return { success: false, message: `Error stopping vectorization: ${(error as Error).message}` };
      }
    });

    ipcMain.handle('vectorization:getStats', async (_, projectId: number) => {
      console.log('[FilesystemHandlers] Getting vectorization stats for project', projectId);

      try {
        const { vectorizationOrchestrator } = await import('../services/vectorization-orchestrator');
        const stats = await vectorizationOrchestrator.getVectorizationStats(projectId);
        return stats;
      } catch (error) {
        console.error('[FilesystemHandlers] Error getting vectorization stats:', error);
        throw error;
      }
    });

    ipcMain.handle('vectorization:getFileInfo', async (_, projectId: number) => {
      console.log('[FilesystemHandlers] Getting file vectorization info for project', projectId);

      try {
        const { vectorizationOrchestrator } = await import('../services/vectorization-orchestrator');
        const fileInfo = await vectorizationOrchestrator.getFileVectorizationInfo(projectId);
        return fileInfo;
      } catch (error) {
        console.error('[FilesystemHandlers] Error getting file vectorization info:', error);
        throw error;
      }
    });

    ipcMain.handle('vectorization:clear', async (_, projectId: number) => {
      console.log('[FilesystemHandlers] Clearing vectors for project', projectId);

      try {
        const { vectorizationOrchestrator } = await import('../services/vectorization-orchestrator');
        await vectorizationOrchestrator.clearVectors(projectId);
        return { success: true, message: 'Vectors cleared successfully.' };
      } catch (error) {
        console.error('[FilesystemHandlers] Error clearing vectors:', error);
        return { success: false, message: `Error clearing vectors: ${(error as Error).message}` };
      }
    });

    ipcMain.handle('vectorization:testSearch', async (_, { projectId, query, k }: {
      projectId: number;
      query: string;
      k?: number
    }) => {
      console.log('[FilesystemHandlers] Testing vector search for project', projectId, 'with query:', query);

      try {
        const { vectorizationOrchestrator } = await import('../services/vectorization-orchestrator');
        const results = await vectorizationOrchestrator.testVectorSearch(projectId, query, k);
        return results;
      } catch (error) {
        console.error('[FilesystemHandlers] Error testing vector search:', error);
        throw error;
      }
    });
  }

  // Recursive function to read directory structure with file sizes
  private async readDirectory(dirPath: string): Promise<any[]> {
    const dirents = await fs.readdir(dirPath, { withFileTypes: true });
    const files = await Promise.all(dirents.map(async (dirent) => {
      const res = path.resolve(dirPath, dirent.name);
      if (dirent.isDirectory()) {
        const children = await this.readDirectory(res);
        // Calculate total size of directory
        const totalSize = children.reduce((sum, child) => sum + (child.size || 0), 0);
        return {
          id: res,
          name: dirent.name,
          children,
          size: totalSize,
          isDirectory: true
        };
      } else {
        try {
          const stats = await fs.stat(res);
          return {
            id: res,
            name: dirent.name,
            size: stats.size,
            isDirectory: false
          };
        } catch (error) {
          // If we can't read the file, return 0 size
          return {
            id: res,
            name: dirent.name,
            size: 0,
            isDirectory: false
          };
        }
      }
    }));

    // Sort directories first, then files, all alphabetically
    files.sort((a, b) => {
      if (a.isDirectory && !b.isDirectory) return -1;
      if (!a.isDirectory && b.isDirectory) return 1;
      return a.name.localeCompare(b.name);
    });

    return files;
  }

  // New lazy loading function with file sizes
  private async readDirectoryLazy(dirPath: string): Promise<any[]> {
    const dirents = await fs.readdir(dirPath, { withFileTypes: true });
    const files = await Promise.all(dirents.map(async (dirent) => {
      const res = path.resolve(dirPath, dirent.name);
      const isDirectory = dirent.isDirectory();
      let hasChildren = false;
      let size = 0;

      if (isDirectory) {
        try {
          const subDirents = await fs.readdir(res, { withFileTypes: true });
          hasChildren = subDirents.length > 0;
        } catch (e) {
          // Ignore errors for sub-directories (e.g. permissions)
        }
      } else {
        try {
          const stats = await fs.stat(res);
          size = stats.size;
        } catch (e) {
          // If we can't read the file, size remains 0
        }
      }

      return {
        id: res,
        name: dirent.name,
        children: isDirectory ? [] : undefined, // Important for lazy loading
        hasChildren: isDirectory && hasChildren,
        size,
        isDirectory
      };
    }));

    // Sort directories first, then files, all alphabetically
    files.sort((a, b) => {
      if (a.hasChildren && !b.hasChildren) return -1;
      if (!a.hasChildren && b.hasChildren) return 1;
      return a.name.localeCompare(b.name);
    });

    return files;
  }

  /**
   * Register Ollama API handlers
   */
  private registerOllamaHandlers(): void {
    // Get all available Ollama models
    ipcMain.handle('ollama:getModels', async () => {
      try {
        const models = await ollamaAPIService.getAvailableModels();
        return {
          success: true,
          models
        };
      } catch (error) {
        console.error('[FilesystemHandlers] Error getting Ollama models:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          models: []
        };
      }
    });

    // Get embedding models specifically
    ipcMain.handle('ollama:getEmbeddingModels', async () => {
      try {
        const models = await ollamaAPIService.getEmbeddingModels();
        return {
          success: true,
          models
        };
      } catch (error) {
        console.error('[FilesystemHandlers] Error getting Ollama embedding models:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          models: []
        };
      }
    });

    // Get LLM models specifically
    ipcMain.handle('ollama:getLLMModels', async () => {
      try {
        const models = await ollamaAPIService.getLLMModels();
        return {
          success: true,
          models
        };
      } catch (error) {
        console.error('[FilesystemHandlers] Error getting Ollama LLM models:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          models: []
        };
      }
    });

    // Check if Ollama server is running
    ipcMain.handle('ollama:checkServer', async () => {
      try {
        const isRunning = await ollamaAPIService.isServerRunning();
        return {
          success: true,
          isRunning
        };
      } catch (error) {
        console.error('[FilesystemHandlers] Error checking Ollama server:', error);
        return {
          success: false,
          isRunning: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
  }
}
