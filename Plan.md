────────────────────────────────────────────────────────
ТЕХНИЧЕСКОЕ ЗАДАНИЕ
RAG Code Assistant – Локальная RAG-система с поддержкой Model Context Protocol (MCP)
версия 1.0 MVP, дата 26.07.2025
────────────────────────────────────────────────────────

1. ЦЕЛЬ
Создать простую **MVP (минимально жизнеспособный продукт)** в форме кроссплатформенного **desktop-приложения** на Electron для личного использования "вайбкодера". Система будет выступать в роли **локального провайдера контекста**, следуя стандарту **Model Context Protocol (MCP)**. Это позволит AI-инструментам кодинга (например, Cursor AI, Claude Dev или GitHub Copilot), поддерживающим MCP, напрямую запрашивать у **RAG Code Assistant** релевантный контекст. Приложение будет декомпозировать запрос, извлекать максимально плотный контекст из всей информации о проекте и полезных доках, а затем прогонять через LLM для генерации улучшенного, релевантного ответа. Всё работает преимущественно локально для приватности, с опцией использования внешних API (Gemini) или локальных моделей (Ollama). MVP фокусируется на простоте, с потенциалом для публикации как open-source проекта для дальнейшего развития.

2. ОБЩИЕ ТРЕБОВАНИЯ
*   **Основное приложение:** Одно исполняемое окно Electron (для управления проектами, RAG и предоставления MCP-совместимого API).
*   **Все данные и вычисления:** Хранятся/выполняются **локально** на устройстве пользователя (документы не уходят в облако).
*   **Внешние зависимости:** Опционально Gemini API (HTTPS) для эмбеддингов/генерации; **основной способ интеграции с AI-инструментами — через локальный HTTP API, соответствующий Model Context Protocol (MCP)**.
*   **Установщик:** ≤ 100 МБ (минимизировано для MVP).
*   **Фокус на простоте:** Начинать с нуля, без лишних интеграций; MCP-совместимый API для запросов — базовый локальный сервер.

3. ФУНКЦИОНАЛЬНОСТЬ

3.1 Проекты
*   Создание / удаление / переключение проектов (для MVP — поддержка 1–2 проектов).
*   Проект = папка на диске + запись в SQLite + коллекция в LanceDB.

3.2 Импорт документов
*   Сканирование каталога (`node:fs`).
*   Базовые фильтры по маске (*.js, *.ts, *.md, *.pdf, ...).
*   Отслеживание изменений (hash + mtime) для автообновления индекса.

3.3 Индексация (RAG-пайплайн)
*   **Чанкинг:** `RecursiveCharacterTextSplitter` (размер и overlap настраиваются, дефолт 1024 токена).
*   **Эмбеддинги:** Опционально **Gemini Embedding 001** (HTTPS-запрос из Main-process) или локальная модель из **Ollama** (e.g., `nomic-embed-text`).
*   **Хранение:** **LanceDB** (embedded, без серверов).
*   Параллельная индексация в фоне через Worker Thread, простой прогресс в UI или логах.

3.4 Обработка запросов (как будет работать проект — твоими и улучшенными словами)
*   **Локальный MCP-совместимый API:** Приложение запускает простой локальный HTTP-сервер (на базе Express.js в Main-process), который будет слушать запросы от AI-инструментов по стандарту MCP (например, `POST http://localhost:PORT/mcp/v1/context`).
*   **Интеграция с AI-инструментами:** AI-инструмент (например, Cursor AI, Claude Dev или GitHub Copilot, если они поддерживают MCP) делает запрос к **RAG Code Assistant** через этот локальный MCP-API, передавая свой текущий контекст или вопрос.
*   **Декомпозиция запроса:** Система берёт запрос, полученный от AI-инструмента, и **декомпозирует** его на мелкие, более конкретные части или подзапросы. Это может быть сделано с помощью простых правил (regex) или базового парсера, например, "fix bug in auth module" может превратиться в "найди текущий auth-код", "проанализируй баги в модуле аутентификации", "собери доки по аутентификации".
*   **RAG-запрос:**
    1.  **Эмбеддинг подзапросов:** Эти мелкие части запроса векторизуются (превращаются в числовые представления), чтобы найти похожие фрагменты в твоих документах.
    2.  **ANN-поиск в LanceDB:** Выполняется поиск ближайших соседей (ANN) в LanceDB (top-k = 5–10), чтобы вытащить **максимально плотный контекст** — всю релевантную информацию о проекте, фрагменты кода, полезные доки, с точными ссылками (файл:строка).
    3.  **Формирование промпта:** Извлечённый плотный контекст объединяется с оригинальным вопросом пользователя в один большой промпт для LLM.
    4.  **Генерация ответа через LLM:** Сформированный промпт отправляется опционально через **Gemini Flash** (для быстрого ответа) или **Ollama** (локально, если нет интернета или нужна полная приватность), чтобы сгенерировать улучшенный ответ — точный, с кодом или советами, основанными на твоих собственных документах.
*   **Вывод:** **RAG Code Assistant** возвращает AI-инструменту ответ в формате, ожидаемом MCP (например, JSON с `context` и `sources`). Таким образом, AI-инструмент получает не просто общий ответ, а **супер-релевантный контекст**, основанный на твоих приватных данных, что позволяет ему генерировать более точные и полезные ответы.

3.5 Экспорт и настройки
*   Экспорт контекста/ответа в Markdown.
*   Настройки: Выбор LLM (Gemini/Ollama), размер чанка, API-ключ (если Gemini).
*   Хранение настроек и ключей через `safeStorage`.

4. ТЕХНОЛОГИЧЕСКИЙ СТЕК

| Слой           | Технология                                | Роль                                     |
| :------------- | :---------------------------------------- | :--------------------------------------- |
| UI             | Electron + React + TypeScript + Material-UI | Отрисовка, навигация                     |
| Main-process   | Node.js 20 + TypeScript + Express.js      | Файлы, БД, RAG-логика, MCP-сервер        |
| Локальная БД   | `better-sqlite3`                          | Проекты, метаданные, история             |
| Векторная БД   | LanceDB (`@lancedb/lancedb`)              | Эмбеддинги, поиск                        |
| Эмбеддинги/LLM | Google Gemini (REST) или Ollama (локальный) | Векторизация и генерация                 |
| Сборка         | Vite + electron-builder                   | Dev-server, упаковка                     |
| Потоки         | Node Worker Threads                       | Индексация без блокировки UI             |

5. АРХИТЕКТУРА ПРОЦЕССОВ

```
┌──────────────────────────┐
│  Renderer (React)        │
│  UI, настройки           │
└──────────┬───────────────┘
           │ IPC (contextBridge)
┌──────────┴───────────────┐
│  Main Process (Node)     │
│  ├── ProjectService      │
│  ├── IndexingWorker     │
│  ├── LanceDBService     │
│  ├── LLMClient (Gemini/Ollama) │
│  ├── MCP Server (Express) │
│  └── SQLite DB          │
└──────────────────────────┘
```

6. СТРУКТУРА ПАПОК

```
rag-code-assistant/
├── src/
│   ├── main/          # Node main-process
│   │   ├── services/
│   │   │   ├── sqlite.ts
│   │   │   ├── lancedb.ts
│   │   │   ├── llm.ts     # Gemini или Ollama
│   │   │   ├── mcp_api.ts # Локальный MCP-сервер (Express)
│   │   │   └── indexer.worker.ts
│   │   └── main.ts
│   ├── renderer/      # React для Electron
│   │   ├── components/
│   │   │   ├── ProjectManager.tsx
│   │   │   ├── QueryInterface.tsx # Для ручного тестирования API
│   │   │   └── Settings.tsx
│   │   └── preload.ts
├── package.json
├── electron-builder.yml
└── assets/
```

7. ПРОТОКОЛЫ И ФОРМАТЫ
*   IPC-API описан в `shared/ipc.d.ts`.
*   Вектор: 768–3072 float32 (зависит от модели) → сохраняется как `Float32Array`.
*   Метаданные чанка: JSON `{file, startLine, endLine, project}`.
*   **Интеграция с AI-инструментами:** Через **Model Context Protocol (MCP)**. Локальный HTTP API будет соответствовать спецификации MCP (например, `POST /mcp/v1/context`). Возвращает JSON в формате MCP (например, `{context: string, sources: Array<{uri: string, text: string}>}`).

8. ПРОИЗВОДИТЕЛЬНОСТЬ И ОГРАНИЧЕНИЯ

| Параметр          | Цель              | Примечание                                     |
| :---------------- | :---------------- | :--------------------------------------------- |
| Макс. размер чанка | 2048 токенов      | Совместимо с Gemini/Ollama                     |
| Индексация        | 50 файлов/мин     | Зависит от аппаратных ресурсов (CPU/GPU для Ollama) |
| Размер БД         | ≤ 1 ГБ на 500k чанков | LanceDB + mmap                                 |
| Latency ответа    | < 5 с             | Для полного цикла (декомпозиция + RAG + LLM) |
| Потоки            | 1 Worker Thread   | Не блокирует UI                                |

9. БЕЗОПАСНОСТЬ
*   API-ключ (если Gemini) шифруется `safeStorage.encryptString`.
*   `nodeIntegration: false`, `contextIsolation: true` в Electron.
*   Локальный MCP API работает только на `127.0.0.1` (localhost), без внешнего доступа.
*   Опционально: Проверка на наличие Ollama для оффлайн-режима.

10. ЭТАПЫ РАЗРАБОТКИ (1–2-недельный план для MVP)

| День/Неделя | Задача                                          | Статус      |
| :---------- | :---------------------------------------------- | :---------- |
| 1–2         | Boilerplate Electron + Vite + React             | ❌ НЕ НАЧАТО |
| 3–5         | SQLite + LanceDB инфраструктура + базовая индексация | ❌ НЕ НАЧАТО |
| 6–8         | **MCP-совместимый локальный API** + декомпозиция + RAG-поиск | ❌ НЕ НАЧАТО |
| 9–10        | LLM-интеграция (Gemini/Ollama) + генерация ответов | ❌ НЕ НАЧАТО |
| 11–12       | Настройки, базовые тесты, экспорт               | ❌ НЕ НАЧАТО |
| 13–14       | Сборка, личное тестирование, опциональная публикация | ❌ НЕ НАЧАТО |

11. КРИТЕРИИ ПРИЕМКИ MVP
*   Приложение запускается на Windows/macOS без внешних зависимостей (кроме Ollama, если выбран).
*   Создаёт проект, индексирует 50 файлов < 1 мин.
*   Обрабатывает запрос через **MCP-совместимый локальный API**, возвращая плотный контекст.
*   Полный цикл (декомпозиция + RAG + LLM) даёт ответ < 5 с с плотным контекстом.
*   Размер установщика ≤ 100 МБ, простая установка.
*   Работает для личного использования: **предоставляет плотный контекст AI-инструментам через MCP**, улучшая их ответы на основе локальных доков.

────────────────────────────────────────
Согласовано: _________________ / 26.07.2025
```