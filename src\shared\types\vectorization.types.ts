/**
 * @file vectorization.types.ts
 * @description Shared types for vectorization functionality across the application
 */

export interface VectorizationConfig {
  provider: 'ollama' | 'gemini';
  embeddingModel: string;
  baseUrl?: string;
  apiKey?: string;
  batchSize?: number;
  maxConcurrency?: number;
}

export interface VectorizationStats {
  totalChunks: number;
  vectorizedChunks: number;
  totalFiles: number;
  vectorizedFiles: number;
  totalVectors: number;
  embeddingDimensions: number;
  indexingTime: number;
  averageTimePerChunk: number;
  provider: string;
  model: string;
  lastUpdated: number;
}

export interface FileVectorizationInfo {
  file: string;
  chunks: number;
  vectorizedChunks: number;
  vectors: number;
  status: 'pending' | 'processing' | 'complete' | 'partial' | 'error';
  lastVectorized?: number;
  error?: string;
}

export interface VectorizationUpdatePayload {
  type: 'progress' | 'file_complete' | 'batch_complete' | 'error';
  projectId: number;
  progress?: number;
  file?: string;
  completedFiles?: number;
  totalFiles?: number;
  stats?: VectorizationStats;
  error?: string;
}

export interface VectorizationState {
  isVectorizing: boolean;
  progress: number;
  currentFile?: string;
  completedFiles: number;
  totalFiles: number;
  stats?: VectorizationStats;
  fileInfo: FileVectorizationInfo[];
  config?: VectorizationConfig;
}

export interface ChunkData {
  id: string;
  content: string;
  metadata: {
    source: string;
    file: string;
    projectId: number;
    loc?: {
      lines: {
        from: number;
        to: number;
      };
    };
  };
  tokenCount: number;
  charCount: number;
}

export interface VectorizationResult {
  success: boolean;
  message: string;
  stats?: VectorizationStats;
}

export interface VectorSearchResult {
  content: string;
  metadata: any;
  score: number;
}

export interface VectorSearchRequest {
  projectId: number;
  query: string;
  k?: number;
  scoreThreshold?: number;
}

// Validation schemas
export const VECTORIZATION_CONFIG_SCHEMA = {
  provider: { required: true, type: 'string', enum: ['ollama', 'gemini'] },
  embeddingModel: { required: true, type: 'string', minLength: 1 },
  baseUrl: { required: false, type: 'string' },
  apiKey: { required: false, type: 'string' },
  batchSize: { required: false, type: 'number', min: 1, max: 1000 },
  maxConcurrency: { required: false, type: 'number', min: 1, max: 10 }
} as const;

export const DEFAULT_VECTORIZATION_CONFIG: VectorizationConfig = {
  provider: 'ollama',
  embeddingModel: 'nomic-embed-text',
  baseUrl: 'http://localhost:11434',
  batchSize: 50,
  maxConcurrency: 3
};

// Type guards
export function isValidVectorizationConfig(config: any): config is VectorizationConfig {
  if (!config || typeof config !== 'object') return false;
  
  const schema = VECTORIZATION_CONFIG_SCHEMA;
  
  for (const [key, rules] of Object.entries(schema)) {
    const value = config[key];
    
    if (rules.required && (value === undefined || value === null)) {
      return false;
    }
    
    if (value !== undefined) {
      if (rules.type === 'string' && typeof value !== 'string') return false;
      if (rules.type === 'number' && typeof value !== 'number') return false;
      
      if (rules.enum && !rules.enum.includes(value)) return false;
      if (rules.minLength && value.length < rules.minLength) return false;
      if (rules.min && value < rules.min) return false;
      if (rules.max && value > rules.max) return false;
    }
  }
  
  return true;
}

export function validateVectorizationConfig(config: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!config || typeof config !== 'object') {
    return { isValid: false, errors: ['Configuration must be an object'] };
  }
  
  const schema = VECTORIZATION_CONFIG_SCHEMA;
  
  for (const [key, rules] of Object.entries(schema)) {
    const value = config[key];
    
    if (rules.required && (value === undefined || value === null)) {
      errors.push(`${key} is required`);
      continue;
    }
    
    if (value !== undefined) {
      if (rules.type === 'string' && typeof value !== 'string') {
        errors.push(`${key} must be a string`);
      } else if (rules.type === 'number' && typeof value !== 'number') {
        errors.push(`${key} must be a number`);
      } else {
        if (rules.enum && !rules.enum.includes(value)) {
          errors.push(`${key} must be one of: ${rules.enum.join(', ')}`);
        }
        if (rules.minLength && value.length < rules.minLength) {
          errors.push(`${key} must be at least ${rules.minLength} characters long`);
        }
        if (rules.min && value < rules.min) {
          errors.push(`${key} must be at least ${rules.min}`);
        }
        if (rules.max && value > rules.max) {
          errors.push(`${key} must be at most ${rules.max}`);
        }
      }
    }
  }
  
  return { isValid: errors.length === 0, errors };
}
