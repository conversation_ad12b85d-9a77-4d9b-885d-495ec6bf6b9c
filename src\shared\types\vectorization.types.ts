/**
 * @file src/shared/types/vectorization.types.ts
 * @description Type definitions for vectorization functionality - STUB VERSION
 * Vectorization functionality has been removed, these are placeholder types.
 */

export interface VectorizationConfig {
  provider: string;
  model: string;
  embeddingModel: string;
  baseUrl?: string;
  apiKey?: string;
  batchSize?: number;
}

export interface VectorizationStats {
  totalChunks: number;
  vectorizedChunks: number;
  totalFiles: number;
  vectorizedFiles: number;
  totalVectors: number;
  embeddingDimensions: number;
  indexingTime: number;
  averageTimePerChunk: number;
  provider: string;
  model: string;
  lastUpdated: number;
}

export interface FileVectorizationInfo {
  file: string;
  chunks: number;
  vectorizedChunks: number;
  vectors: number;
  status: 'pending' | 'partial' | 'complete';
}

export interface VectorizationUpdatePayload {
  type: 'progress' | 'file_complete' | 'batch_complete' | 'error';
  projectId: number;
  file?: string;
  progress?: number;
  completedFiles?: number;
  totalFiles?: number;
  stats?: VectorizationStats;
  error?: string;
}

export interface ChunkData {
  id: string;
  content: string;
  metadata: {
    file: string;
    chunkIndex: number;
    startLine?: number;
    endLine?: number;
    [key: string]: any;
  };
}

/**
 * Default vectorization configuration
 */
export const DEFAULT_VECTORIZATION_CONFIG: VectorizationConfig = {
  provider: 'ollama',
  model: 'llama3.2',
  embeddingModel: 'nomic-embed-text',
  baseUrl: 'http://localhost:11434',
  batchSize: 10
};

/**
 * Validate vectorization configuration - STUB IMPLEMENTATION
 */
export function validateVectorizationConfig(_config: Partial<VectorizationConfig>): {
  isValid: boolean;
  errors: string[];
} {
  return {
    isValid: false,
    errors: ['Vectorization functionality has been removed']
  };
}
