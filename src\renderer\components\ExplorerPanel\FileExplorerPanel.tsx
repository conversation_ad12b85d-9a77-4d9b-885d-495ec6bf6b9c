/**
 * @file src/renderer/components/FileExplorerPanel.tsx
 * @description File explorer panel component for browsing project files
 */

import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import { FileTree } from './FileTree';
import { ChunkingPanel } from './ChunkingPanel';
import { useAppStore } from '../../../shared/store';

interface FileExplorerPanelProps {
  project: any | null;
  onSelectionChange: (selection: Set<string>) => void;
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B (0 chars)';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const sizeStr = parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  return `${sizeStr} (${bytes.toLocaleString()} chars)`;
};

export const FileExplorerPanel: React.FC<FileExplorerPanelProps> = ({
  project,
  onSelectionChange
}) => {
  // Use store instead of local state
  const {
    selectedFilePaths,
    totalSelectedSize,
    totalSelectedTokens
  } = useAppStore();

  const [selectedFileCount, setSelectedFileCount] = useState(0);
  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>

      
      {project ? (
        <Box sx={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0 // Важно для правильного flex-shrink
        }}>
          <Typography
            variant="body2"
            sx={{
              color: '#94a3b8',
              mb: 2,
              fontFamily: 'monospace',
              backgroundColor: 'rgba(147, 51, 234, 0.1)',
              p: 1,
              borderRadius: 1,
              flexShrink: 0 // Не сжимать этот элемент
            }}
          >
            {project.path}
          </Typography>
          <Box sx={{
            flex: 1,
            overflow: 'hidden',
            minHeight: 0 // Важно для правильного flex-shrink
          }}>
            <FileTree
              project={project}
              onSelectionChange={onSelectionChange}
              onSelectedFilesChange={(filePaths) => {
                setSelectedFileCount(filePaths.length);
              }}
            />
          </Box>

          {/* Chunking Panel */}
          {selectedFileCount > 0 && (
            <Box sx={{ mt: 2, flexShrink: 0 }}>
              <ChunkingPanel
                project={project}
                selectedFileCount={selectedFileCount}
                selectedFilePaths={selectedFilePaths}
              />
            </Box>
          )}
        </Box>
      ) : (
        <Box sx={{ 
          flex: 1, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          textAlign: 'center'
        }}>
          <Typography variant="body1" sx={{ color: '#64748b' }}>
            No project selected. Please select a project from the Projects panel.
          </Typography>
        </Box>
      )}
    </Box>
  );
};
