// FILE: package.json

{
  "name": "smart-rag",
  "productName": "SmartRAG",
  "version": "1.0.0",
  "description": "RAG Code Assistant with MCP support",
  "main": "dist-electron/main.js",
  "scripts": {
    "dev": "vite",
    "build": "tsc && vite build && electron-builder",
    "test": "jest",
    "test:watch": "jest --watch",
    "gg": "ts-node _gg.ts"
  },
  "author": "AETHERIUM SWARM",
  "license": "MIT",
  "dependencies": {
    "@emotion/react": "^11.14.0",
    "@emotion/styled": "^11.14.1",
    "@lancedb/lancedb": "^0.12.0",
    "@langchain/community": "^0.3.49",
    "@langchain/core": "^0.3.66",
    "@langchain/openai": "^0.6.3",
    "@mui/icons-material": "^7.2.0",
    "@mui/material": "^7.2.0",
    "@tailwindcss/postcss": "^4.1.11",
    "@tanstack/react-virtual": "^3.13.12",
    "antd": "^5.26.6",
    "better-sqlite3": "^11.10.0",
    "date-fns": "^4.1.0",
    "electron-squirrel-startup": "^1.0.1",
    "express": "^5.1.0",
    "js-tiktoken": "^1.0.20",
    "langchain": "^0.3.30",
    "react": "^19.1.0",
    "react-arborist": "^3.4.3",
    "react-dom": "^19.1.0",
    "tiktoken": "^1.0.21",
    "use-debounce": "^10.0.5",
    "zustand": "^5.0.6"
  },
  "devDependencies": {
    "@electron/rebuild": "^4.0.1",
    "@types/better-sqlite3": "^7.6.13",
    "@types/express": "^5.0.3",
    "@types/jest": "^30.0.0",
    "@types/node": "^24.1.0",
    "@types/react": "^19.1.8",
    "@types/react-dom": "^19.1.6",
    "@vitejs/plugin-react": "^4.7.0",
    "chalk": "^5.4.1",
    "concurrently": "^9.2.0",
    "electron": "^37.2.4",
    "electron-builder": "^26.0.12",
    "glob": "^11.0.3",
    "jest": "^30.0.5",
    "npm-check-updates": "^18.0.2",
    "tailwindcss": "^4.0.0",
    "ts-jest": "^29.4.0",
    "ts-node": "^10.9.2",
    "typescript": "^5.8.3",
    "vite": "^7.0.6",
    "vite-plugin-electron": "^0.29.0",
    "vite-plugin-electron-renderer": "^0.14.6",
    "wait-on": "^8.0.4"
  }
}


---

// FILE: src\main\ipc\filesystem-handlers.ts

/**
 * @file src/main/ipc/filesystem-handlers.ts
 * @description IPC handlers for filesystem operations and vectorization.
 * Handles directory reading, file operations, and vectorization management.
 */

import { ipcMain, dialog } from 'electron';
import fs from 'fs/promises';
import path from 'path';
import { sqliteService } from '../services/sqlite';
import { WindowManager } from '../managers/window-manager';
import { ollamaAPIService } from '../services/ollama-api';

export class FilesystemHandlers {
  private windowManager: WindowManager;

  constructor(windowManager: WindowManager) {
    this.windowManager = windowManager;
    this.registerHandlers();
    this.registerOllamaHandlers();
  }

  private registerHandlers(): void {
    // Dialog handlers
    ipcMain.handle('dialog:openDirectory', async () => {
      console.log('[FilesystemHandlers] Opening directory dialog...');
      try {
        const mainWindow = this.windowManager.getMainWindow();
        if (!mainWindow) {
          throw new Error('Main window not available');
        }

        const { canceled, filePaths } = await dialog.showOpenDialog(mainWindow, {
          properties: ['openDirectory']
        });
        console.log('[FilesystemHandlers] Dialog result:', { canceled, filePaths });
        if (canceled) return null;
        return filePaths[0];
      } catch (error) {
        console.error('[FilesystemHandlers] Error opening directory dialog:', error);
        return null;
      }
    });

    // File system handlers
    ipcMain.handle('fs:readDirectory', async (_, dirPath: string) => {
      try {
        return await this.readDirectory(dirPath);
      } catch (error) {
        console.error(`Failed to read directory ${dirPath}:`, error);
        return null;
      }
    });

    ipcMain.handle('fs:readDirectoryLazy', async (_, dirPath: string) => {
      try {
        return await this.readDirectoryLazy(dirPath);
      } catch (error) {
        console.error(`Failed to read directory lazily ${dirPath}:`, error);
        return null;
      }
    });

    ipcMain.handle('fs:readFile', async (_, filePath: string) => {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        return content;
      } catch (error) {
        console.error(`Failed to read file ${filePath}:`, error);
        throw error;
      }
    });

    ipcMain.handle('fs:getTokenCount', async (_, filePath: string) => {
      try {
        const content = await fs.readFile(filePath, 'utf-8');
        // Simple token estimation: ~4 characters per token
        return Math.ceil(content.length / 4);
      } catch (error) {
        // For binary files or files that can't be read, return 0
        return 0;
      }
    });

    // Vectorization handlers
    this.registerVectorizationHandlers();

    // App data management
    ipcMain.handle('app:clearData', async () => {
      try {
        const { app } = require('electron');
        const fsSync = require('fs').promises;
        const pathModule = require('path');

        // Получаем пути к данным приложения
        const userDataPath = app.getPath('userData');
        const appDataPath = app.getPath('appData');

        console.log('[FilesystemHandlers] Clearing app data...');
        console.log('[FilesystemHandlers] UserData path:', userDataPath);

        // Очищаем базу данных SQLite
        sqliteService.clearAllData();

        // Очищаем кеш и временные файлы
        const cachePath = pathModule.join(userDataPath, 'cache');
        const tempPath = pathModule.join(userDataPath, 'temp');

        try {
          await fsSync.rmdir(cachePath, { recursive: true });
          console.log('[FilesystemHandlers] Cleared cache directory');
        } catch (error) {
          console.log('[FilesystemHandlers] Cache directory not found or already cleared');
        }

        try {
          await fsSync.rmdir(tempPath, { recursive: true });
          console.log('[FilesystemHandlers] Cleared temp directory');
        } catch (error) {
          console.log('[FilesystemHandlers] Temp directory not found or already cleared');
        }

        console.log('[FilesystemHandlers] App data cleared successfully');
      } catch (error) {
        console.error('[FilesystemHandlers] Error clearing app data:', error);
        throw error;
      }
    });

    console.log('[FilesystemHandlers] All filesystem IPC handlers registered');
  }

  private registerVectorizationHandlers(): void {
    // Векторизация - функциональность удалена
    ipcMain.handle('vectorization:start', async () => {
      return { success: false, message: 'Vectorization functionality has been removed' };
    });

    ipcMain.handle('vectorization:stop', async () => {
      return { success: false, message: 'Vectorization functionality has been removed' };
    });

    ipcMain.handle('vectorization:getStats', async () => {
      return {
        totalChunks: 0,
        vectorizedChunks: 0,
        totalFiles: 0,
        vectorizedFiles: 0,
        totalVectors: 0,
        embeddingDimensions: 0,
        indexingTime: 0,
        averageTimePerChunk: 0,
        provider: 'removed',
        model: 'removed',
        lastUpdated: Date.now()
      };
    });

    ipcMain.handle('vectorization:getFileInfo', async () => {
      return [];
    });

    ipcMain.handle('vectorization:clear', async () => {
      return { success: false, message: 'Vectorization functionality has been removed' };
    });

    ipcMain.handle('vectorization:testSearch', async () => {
      return [];
    });
  }

  // Recursive function to read directory structure with file sizes
  private async readDirectory(dirPath: string): Promise<any[]> {
    const dirents = await fs.readdir(dirPath, { withFileTypes: true });
    const files = await Promise.all(dirents.map(async (dirent) => {
      const res = path.resolve(dirPath, dirent.name);
      if (dirent.isDirectory()) {
        const children = await this.readDirectory(res);
        // Calculate total size of directory
        const totalSize = children.reduce((sum, child) => sum + (child.size || 0), 0);
        return {
          id: res,
          name: dirent.name,
          children,
          size: totalSize,
          isDirectory: true
        };
      } else {
        try {
          const stats = await fs.stat(res);
          return {
            id: res,
            name: dirent.name,
            size: stats.size,
            isDirectory: false
          };
        } catch (error) {
          // If we can't read the file, return 0 size
          return {
            id: res,
            name: dirent.name,
            size: 0,
            isDirectory: false
          };
        }
      }
    }));

    // Sort directories first, then files, all alphabetically
    files.sort((a, b) => {
      if (a.isDirectory && !b.isDirectory) return -1;
      if (!a.isDirectory && b.isDirectory) return 1;
      return a.name.localeCompare(b.name);
    });

    return files;
  }

  // New lazy loading function with file sizes
  private async readDirectoryLazy(dirPath: string): Promise<any[]> {
    const dirents = await fs.readdir(dirPath, { withFileTypes: true });
    const files = await Promise.all(dirents.map(async (dirent) => {
      const res = path.resolve(dirPath, dirent.name);
      const isDirectory = dirent.isDirectory();
      let hasChildren = false;
      let size = 0;

      if (isDirectory) {
        try {
          const subDirents = await fs.readdir(res, { withFileTypes: true });
          hasChildren = subDirents.length > 0;
        } catch (e) {
          // Ignore errors for sub-directories (e.g. permissions)
        }
      } else {
        try {
          const stats = await fs.stat(res);
          size = stats.size;
        } catch (e) {
          // If we can't read the file, size remains 0
        }
      }

      return {
        id: res,
        name: dirent.name,
        children: isDirectory ? [] : undefined, // Important for lazy loading
        hasChildren: isDirectory && hasChildren,
        size,
        isDirectory
      };
    }));

    // Sort directories first, then files, all alphabetically
    files.sort((a, b) => {
      if (a.hasChildren && !b.hasChildren) return -1;
      if (!a.hasChildren && b.hasChildren) return 1;
      return a.name.localeCompare(b.name);
    });

    return files;
  }

  /**
   * Register Ollama API handlers
   */
  private registerOllamaHandlers(): void {
    // Get all available Ollama models
    ipcMain.handle('ollama:getModels', async () => {
      try {
        const models = await ollamaAPIService.getAvailableModels();
        return {
          success: true,
          models
        };
      } catch (error) {
        console.error('[FilesystemHandlers] Error getting Ollama models:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          models: []
        };
      }
    });

    // Get embedding models specifically
    ipcMain.handle('ollama:getEmbeddingModels', async () => {
      try {
        const models = await ollamaAPIService.getEmbeddingModels();
        return {
          success: true,
          models
        };
      } catch (error) {
        console.error('[FilesystemHandlers] Error getting Ollama embedding models:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          models: []
        };
      }
    });

    // Get LLM models specifically
    ipcMain.handle('ollama:getLLMModels', async () => {
      try {
        const models = await ollamaAPIService.getLLMModels();
        return {
          success: true,
          models
        };
      } catch (error) {
        console.error('[FilesystemHandlers] Error getting Ollama LLM models:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error),
          models: []
        };
      }
    });

    // Check if Ollama server is running
    ipcMain.handle('ollama:checkServer', async () => {
      try {
        const isRunning = await ollamaAPIService.isServerRunning();
        return {
          success: true,
          isRunning
        };
      } catch (error) {
        console.error('[FilesystemHandlers] Error checking Ollama server:', error);
        return {
          success: false,
          isRunning: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });
  }
}


---

// FILE: src\main\ipc\ipc-router.ts

/**
 * @file src/main/ipc/ipc-router.ts
 * @description Central IPC router that coordinates all IPC handlers.
 * Provides a single entry point for IPC communication setup.
 */

import { WindowManager } from '../managers/window-manager';
import { WorkerManager } from '../managers/worker-manager';
import { LangChainHandlers } from './langchain-handlers';
import { ProjectHandlers } from './project-handlers';
import { FilesystemHandlers } from './filesystem-handlers';

export class IPCRouter {
  private windowManager: WindowManager;
  private workerManager: WorkerManager;
  private langChainHandlers: LangChainHandlers;
  private projectHandlers: ProjectHandlers;
  private filesystemHandlers: FilesystemHandlers;

  constructor(windowManager: WindowManager, workerManager: WorkerManager) {
    this.windowManager = windowManager;
    this.workerManager = workerManager;
    
    // Initialize all handler groups
    this.langChainHandlers = new LangChainHandlers(this.workerManager);
    this.projectHandlers = new ProjectHandlers(this.windowManager);
    this.filesystemHandlers = new FilesystemHandlers(this.windowManager);

    console.log('[IPCRouter] All IPC handlers initialized successfully');
  }

  /**
   * Initialize vectorization orchestrator - FUNCTIONALITY REMOVED
   */
  async initializeVectorization(): Promise<void> {
    console.log('[IPCRouter] Vectorization functionality has been removed');
  }

  /**
   * Get all handler instances for testing or advanced usage
   */
  getHandlers() {
    return {
      langChain: this.langChainHandlers,
      project: this.projectHandlers,
      filesystem: this.filesystemHandlers
    };
  }
}


---

// FILE: src\main\ipc\langchain-handlers.ts

/**
 * @file src/main/ipc/langchain-handlers.ts
 * @description IPC handlers for LangChain RAG operations - REMOVED.
 * LangChain functionality has been removed from the project.
 */

import { ipcMain } from 'electron';
import { WorkerManager } from '../managers/worker-manager';

export class LangChainHandlers {
  private workerManager: WorkerManager;

  constructor(workerManager: WorkerManager) {
    this.workerManager = workerManager;
    this.registerHandlers();
  }

  private registerHandlers(): void {
    // LangChain functionality has been removed
    ipcMain.handle('langchain:indexSelectedFiles', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:indexFile', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:reindexFile', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:query', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:getProjectStats', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:updateConfig', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:cancelIndexing', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:clearProjectChunks', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    console.log('[LangChainHandlers] LangChain IPC handlers registered (functionality removed)');
  }
}


---

// FILE: src\main\ipc\project-handlers.ts

/**
 * @file src/main/ipc/project-handlers.ts
 * @description IPC handlers for project management operations.
 * Handles project CRUD operations and project file management.
 */

import { ipcMain, dialog } from 'electron';
import { basename } from 'path';
import { sqliteService } from '../services/sqlite';
import { projectFilesService } from '../services/project-files';
import { WindowManager } from '../managers/window-manager';

export class ProjectHandlers {
  private windowManager: WindowManager;

  constructor(windowManager: WindowManager) {
    this.windowManager = windowManager;
    this.registerHandlers();
  }

  private registerHandlers(): void {
    // Project management handlers
    ipcMain.handle('projects:getAll', async () => {
      return sqliteService.getAllProjects();
    });

    ipcMain.handle('projects:add', async (_, projectPath: string) => {
      console.log('[ProjectHandlers] Adding project:', projectPath);
      try {
        const name = basename(projectPath);
        console.log('[ProjectHandlers] Project name:', name);

        // Check for duplicates
        if (sqliteService.getProjectByName(name)) {
          throw new Error(`Project with name "${name}" already exists.`);
        }

        sqliteService.addProject(name, projectPath);
        const newProject = sqliteService.getProjectByName(name);
        console.log('[ProjectHandlers] Project added successfully:', newProject);
        return newProject;
      } catch (error: any) {
        console.error('[ProjectHandlers] Failed to add project:', error);
        const mainWindow = this.windowManager.getMainWindow();
        if (mainWindow) {
          dialog.showErrorBox('Error Adding Project', error.message);
        }
        return null;
      }
    });

    ipcMain.handle('projects:delete', async (_, id: number) => {
      sqliteService.deleteProject(id);
    });

    // Project files handlers
    ipcMain.handle('projectFiles:addSelected', async (_, payload: { 
      filePaths: string[]; 
      projectId: number; 
      copyFiles?: boolean 
    }) => {
      try {
        return await projectFilesService.addSelectedFiles(payload);
      } catch (error) {
        console.error('[ProjectHandlers] Error adding selected files:', error);
        throw error;
      }
    });

    ipcMain.handle('projectFiles:getSelected', async (_, projectId: number) => {
      try {
        return await projectFilesService.getSelectedFiles(projectId);
      } catch (error) {
        console.error('[ProjectHandlers] Error getting selected files:', error);
        return [];
      }
    });

    ipcMain.handle('projectFiles:removeSelected', async (_, projectId: number, fileId: string) => {
      try {
        await projectFilesService.removeSelectedFile(projectId, fileId);
      } catch (error) {
        console.error('[ProjectHandlers] Error removing selected file:', error);
        throw error;
      }
    });

    ipcMain.handle('projectFiles:clearSelected', async (_, projectId: number) => {
      try {
        await projectFilesService.clearSelectedFiles(projectId);
      } catch (error) {
        console.error('[ProjectHandlers] Error clearing selected files:', error);
        throw error;
      }
    });

    console.log('[ProjectHandlers] All project IPC handlers registered');
  }
}


---

// FILE: src\main\main.ts

/**
 * @file src/main/main.ts
 * @description Main process entry point for the Electron application.
 * Uses modular architecture with dedicated managers and IPC handlers.
 */

import { app } from 'electron';
import { AppManager } from './managers/app-manager';

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

// Initialize the application manager
const appManager = new AppManager();

// All functionality has been moved to AppManager and its components
console.log('[Main] SmartRAG application starting with modular architecture...');






---

// FILE: src\main\managers\app-manager.ts

/**
 * @file src/main/managers/app-manager.ts
 * @description Main application manager that coordinates all components.
 * Handles application lifecycle, menu creation, and component initialization.
 */

import { app, Menu, session } from 'electron';
import { WindowManager } from './window-manager';
import { WorkerManager } from './worker-manager';
import { IPCRouter } from '../ipc/ipc-router';
import { mcpApiService } from '../services/mcp_api';
import { isMac } from '../utils/environment';

export class AppManager {
  private windowManager: WindowManager;
  private workerManager: WorkerManager;
  private ipcRouter: IPCRouter;
  private isDev: boolean;
  private viteDevServerUrl: string | null;

  constructor() {
    // Environment setup
    this.isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;
    this.viteDevServerUrl = process.env['VITE_DEV_SERVER_URL'] || (this.isDev ? 'http://localhost:5173' : null);

    console.log('[AppManager] Environment check:');
    console.log('[AppManager] NODE_ENV:', process.env.NODE_ENV);
    console.log('[AppManager] isPackaged:', app.isPackaged);
    console.log('[AppManager] isDev:', this.isDev);
    console.log('[AppManager] VITE_DEV_SERVER_URL:', this.viteDevServerUrl);

    // Initialize managers
    this.windowManager = new WindowManager(this.isDev, this.viteDevServerUrl);
    this.workerManager = new WorkerManager(this.windowManager);
    this.ipcRouter = new IPCRouter(this.windowManager, this.workerManager);

    this.setupEventHandlers();
  }

  /**
   * Initialize the application
   */
  async initialize(): Promise<void> {
    // Set up Content Security Policy
    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            this.isDev
              ? "default-src 'self' 'unsafe-inline' data:; script-src 'self' 'unsafe-eval' 'unsafe-inline' data:"
              : "default-src 'self'"
          ]
        }
      });
    });

    this.createApplicationMenu();
    await this.windowManager.createWindow();
    this.workerManager.createLangChainWorker();
    await this.ipcRouter.initializeVectorization();
    mcpApiService.start();

    console.log('[AppManager] Application initialized successfully');
  }

  /**
   * Setup application event handlers
   */
  private setupEventHandlers(): void {
    app.on('ready', async () => {
      await this.initialize();
    });

    app.on('window-all-closed', () => {
      mcpApiService.stop();
      this.workerManager.terminateAll();
      if (process.platform !== 'darwin') app.quit();
    });

    app.on('activate', async () => {
      const { BrowserWindow } = require('electron');
      if (BrowserWindow.getAllWindows().length === 0) {
        await this.windowManager.createWindow();
      }
    });
  }

  /**
   * Create application menu
   */
  private createApplicationMenu(): void {
    const template = [
      ...(isMac ? [{
        label: app.getName(),
        submenu: [
          { role: 'about' },
          { type: 'separator' },
          { role: 'services' },
          { type: 'separator' },
          { role: 'hide' },
          { role: 'hideOthers' },
          { role: 'unhide' },
          { type: 'separator' },
          { role: 'quit' }
        ]
      }] : []),
      {
        label: 'File',
        submenu: [
          isMac ? { role: 'close' } : { role: 'quit' }
        ]
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template as any);
    Menu.setApplicationMenu(menu);
  }

  /**
   * Get manager instances for advanced usage
   */
  getManagers() {
    return {
      window: this.windowManager,
      worker: this.workerManager,
      ipc: this.ipcRouter
    };
  }
}


---

// FILE: src\main\managers\window-manager.ts

/**
 * @file src/main/managers/window-manager.ts
 * @description Window management for the Electron application.
 * Handles window creation, lifecycle, and dev server loading.
 */

import { BrowserWindow, session } from 'electron';
import path from 'path';

export class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private isDev: boolean;
  private viteDevServerUrl: string | null;

  constructor(isDev: boolean, viteDevServerUrl: string | null) {
    this.isDev = isDev;
    this.viteDevServerUrl = viteDevServerUrl;
  }

  /**
   * Create the main application window
   */
  async createWindow(): Promise<BrowserWindow> {
    // Set up Content Security Policy for security
    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            this.isDev
              ? "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: http://localhost:*; script-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:*; style-src 'self' 'unsafe-inline' http://localhost:*;"
              : "default-src 'self' data: blob:; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:;"
          ]
        }
      });
    });

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true, // Explicitly enable web security
      },
    });

    if (this.viteDevServerUrl) {
      console.log('[WindowManager] Attempting to load Vite dev server:', this.viteDevServerUrl);
      await this.loadDevServer();
      this.mainWindow.webContents.openDevTools();
    } else {
      console.log('[WindowManager] Loading production build...');
      this.mainWindow.loadFile(path.join(__dirname, '../index.html'));
      this.mainWindow.webContents.openDevTools();
    }

    return this.mainWindow;
  }

  /**
   * Load development server with retry logic
   */
  private async loadDevServer(retries = 2, delay = 1500): Promise<void> {
    console.log(`[WindowManager] Starting dev server connection attempts to: ${this.viteDevServerUrl}`);
    
    for (let i = 0; i < retries; i++) {
      try {
        console.log(`[WindowManager] Attempting to load Vite dev server (attempt ${i + 1}/${retries})...`);
        
        // Загружаем URL напрямую без fetch проверки
        await this.mainWindow!.loadURL(this.viteDevServerUrl!);
        console.log('[WindowManager] ✅ Successfully loaded Vite dev server');
        return;
      } catch (error) {
        console.log(`[WindowManager] ❌ Failed to load Vite dev server (attempt ${i + 1}/${retries}):`, error);
        if (i < retries - 1) {
          console.log(`[WindowManager] ⏳ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    console.error('[WindowManager] ❌ Failed to load Vite dev server after all retries.');
    console.log('[WindowManager] 🔄 Attempting to load fallback (production build)...');
    
    // Fallback to production build if available
    try {
      const fallbackPath = path.join(__dirname, '../index.html');
      console.log('[WindowManager] Fallback path:', fallbackPath);
      await this.mainWindow!.loadFile(fallbackPath);
      console.log('[WindowManager] ✅ Fallback loaded successfully');
    } catch (fallbackError) {
      console.error('[WindowManager] ❌ Failed to load fallback file:', fallbackError);
      // Последний fallback - простой HTML
      this.mainWindow!.loadURL('data:text/html;charset=utf-8,<html><body style="background:#1e1e1e;color:white;padding:20px;font-family:Arial;"><h1>❌ Ошибка загрузки</h1><p>Не удалось загрузить ни dev сервер, ни production файлы.</p><p>Проверьте консоль для деталей.</p></body></html>');
    }
  }

  /**
   * Get the main window instance
   */
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  /**
   * Send message to renderer process
   */
  sendToRenderer(channel: string, data: any): void {
    this.mainWindow?.webContents.send(channel, data);
  }
}


---

// FILE: src\main\managers\worker-manager.ts

/**
 * @file src/main/managers/worker-manager.ts
 * @description Worker thread management for background processing.
 * LangChain workers have been removed - this is now a placeholder.
 */

import { WindowManager } from './window-manager';

export class WorkerManager {
  private windowManager: WindowManager;

  constructor(windowManager: WindowManager) {
    this.windowManager = windowManager;
  }

  /**
   * Create workers - LangChain functionality removed
   */
  createLangChainWorker(): void {
    console.log('[WorkerManager] LangChain worker functionality has been removed');
  }

  /**
   * Terminate all workers - placeholder
   */
  terminateAll(): void {
    console.log('[WorkerManager] No workers to terminate');
  }
}


---

// FILE: src\main\services\langchain-indexer.worker.ts

/**
 * LangChain-integrated Indexer Worker
 * Background document indexing using LangChain RAG service
 */

import { parentPort } from 'worker_threads';
import fs from 'fs/promises';
import fsSync from 'fs';
import path from 'path';
import crypto from 'crypto';
import { LangChainRAGService, RAGConfig } from './langchain-rag';
import { sqliteService } from './sqlite';

interface IndexingPayload {
  filePath: string;
  projectId: number;
  config?: Partial<RAGConfig>;
}

interface IndexingBatchPayload {
  filePaths: string[];
  projectId: number;
  config?: Partial<RAGConfig>;
}

import { IndexingProgressCallback, IndexingResult } from './langchain-rag';

interface WorkerMessage {
  status: 'progress' | 'completed' | 'error' | 'batch_progress' | 'batch_completed' | 'cancelled';
  file?: string;
  progress?: number;
  error?: string;
  result?: any;
  batchProgress?: {
    completed: number;
    total: number;
    currentFile: string;
    startTime: number;
    elapsedTime: number;
    estimatedRemainingTime: number;
  };
}

// Global cancellation flag
let isCancelled = false;

// Initialize RAG service with default configuration (2025 best practices)
const defaultConfig: RAGConfig = {
  llm: {
    provider: 'ollama',
    model: 'llama3:8b',
    embeddingModel: 'nomic-embed-text',
    baseUrl: 'http://localhost:11434',
  },
  chunking: {
    chunkSize: 512,       // 2025 best practice: 512 tokens
    chunkOverlap: 102,    // 2025 best practice: 20% overlap (512 * 0.2 = 102)
  },
  retrieval: {
    topK: 10,
    scoreThreshold: 0.1,
  },
};

let ragService = new LangChainRAGService(defaultConfig);

/**
 * Process a single file
 */
async function processFile(payload: IndexingPayload): Promise<void> {
  const { filePath, projectId, config } = payload;

  // Reset cancellation flag for new file processing
  isCancelled = false;

  try {
    // Check for cancellation at the start
    if (isCancelled) {
      console.log(`[LangChain Worker] ⏹️ File processing cancelled: ${filePath}`);
      return;
    }

    parentPort?.postMessage({
      status: 'progress',
      file: filePath,
      progress: 10,
    } as WorkerMessage);

    console.log(`[LangChain Worker] Indexing: ${filePath} for project ${projectId}`);

    // Update RAG service config if provided
    if (config) {
      ragService.updateConfig(config);
    }

    // Verify project exists
    const project = sqliteService.getProjectById(projectId);
    if (!project?.dataPath) {
      throw new Error(`Project with ID ${projectId} not found or has no dataPath`);
    }

    // Check for cancellation before heavy operations
    if (isCancelled) {
      console.log(`[LangChain Worker] ⏹️ File processing cancelled: ${filePath}`);
      return;
    }

    parentPort?.postMessage({
      status: 'progress',
      file: filePath,
      progress: 20,
    } as WorkerMessage);

    // Check if file exists
    if (!fsSync.existsSync(filePath)) {
      throw new Error(`File not found: ${filePath}`);
    }

    parentPort?.postMessage({
      status: 'progress',
      file: filePath,
      progress: 30,
    } as WorkerMessage);

    // Index the file using LangChain RAG service
    const indexingResult = await ragService.indexFile(projectId, filePath);

    // Check for cancellation after indexing
    if (isCancelled) {
      console.log(`[LangChain Worker] ⏹️ File processing cancelled after indexing: ${filePath}`);
      return;
    }

    parentPort?.postMessage({
      status: 'progress',
      file: filePath,
      progress: 80,
    } as WorkerMessage);

    // Save indexing metadata
    await saveIndexingMetadata(projectId, filePath, indexingResult);

    parentPort?.postMessage({
      status: 'completed',
      file: filePath,
      progress: 100,
      result: indexingResult,
    } as WorkerMessage);

    console.log(`[LangChain Worker] ✅ Completed indexing ${filePath}:`, indexingResult);

  } catch (error) {
    console.error(`[LangChain Worker] ❌ Error processing ${filePath}:`, error);
    parentPort?.postMessage({
      status: 'error',
      file: filePath,
      error: error instanceof Error ? error.message : String(error),
    } as WorkerMessage);
  }
}

/**
 * Process multiple files in batch
 */
async function processBatch(payload: IndexingBatchPayload): Promise<void> {
  const { filePaths, projectId, config } = payload;

  // Reset cancellation flag for new batch processing
  isCancelled = false;

  try {
    // Check for cancellation at the start
    if (isCancelled) {
      console.log(`[LangChain Worker] ⏹️ Batch processing cancelled before start`);
      return;
    }

    console.log(`[LangChain Worker] Starting batch indexing: ${filePaths.length} files for project ${projectId}`);

    // Update RAG service config if provided
    if (config) {
      ragService.updateConfig(config);
    }

    // Verify project exists
    const project = sqliteService.getProjectById(projectId);
    if (!project?.dataPath) {
      throw new Error(`Project with ID ${projectId} not found or has no dataPath`);
    }

    // Filter existing files
    const existingFiles = filePaths.filter(filePath => fsSync.existsSync(filePath));
    if (existingFiles.length !== filePaths.length) {
      console.warn(`[LangChain Worker] Some files not found. Processing ${existingFiles.length}/${filePaths.length} files`);
    }

    // Check for cancellation before heavy operations
    if (isCancelled) {
      console.log(`[LangChain Worker] ⏹️ Batch processing cancelled before indexing`);
      return;
    }

    const startTime = Date.now();
    // Define the progress callback
    const onProgress: IndexingProgressCallback = (progress) => {
      const elapsedTime = Date.now() - startTime;
      const timePerFile = progress.completed > 0 ? elapsedTime / progress.completed : 0;
      const estimatedRemainingTime = timePerFile * (progress.total - progress.completed);

      parentPort?.postMessage({
        status: 'batch_progress',
        batchProgress: {
          ...progress,
          startTime,
          elapsedTime,
          estimatedRemainingTime,
        },
        progress: (progress.completed / progress.total) * 100,
      } as WorkerMessage);
    };

    // Index all files with progress reporting and cancellation support
    const indexingResult = await ragService.indexFiles(
      projectId,
      existingFiles,
      onProgress,
      () => isCancelled, // Pass cancellation check function
      true // Clear existing chunks for full reindexing
    );

    // Check for cancellation after indexing
    if (isCancelled) {
      console.log(`[LangChain Worker] ⏹️ Batch processing cancelled after indexing`);
      return;
    }

    // Save batch metadata
    await saveBatchMetadata(projectId, existingFiles, indexingResult);

    parentPort?.postMessage({
      status: 'batch_completed',
      result: indexingResult,
    } as WorkerMessage);

    console.log(`[LangChain Worker] ✅ Completed batch indexing:`, indexingResult);

  } catch (error) {
    console.error(`[LangChain Worker] ❌ Error processing batch:`, error);
    parentPort?.postMessage({
      status: 'error',
      error: error instanceof Error ? error.message : String(error),
    } as WorkerMessage);
  }
}

/**
 * Save indexing metadata for a single file
 */
async function saveIndexingMetadata(
  projectId: number,
  filePath: string,
  result: any
): Promise<void> {
  const project = sqliteService.getProjectById(projectId);
  if (!project?.dataPath) return;

  const metadataPath = path.join(project.dataPath, 'metadata', 'langchain_index.json');
  await fs.mkdir(path.dirname(metadataPath), { recursive: true });

  // Load existing metadata
  let existingMetadata: any[] = [];
  try {
    const existingData = await fs.readFile(metadataPath, 'utf-8');
    existingMetadata = JSON.parse(existingData);
  } catch (error) {
    // File doesn't exist, start fresh
  }

  // Create file hash for change detection
  const content = await fs.readFile(filePath, 'utf-8');
  const fileHash = crypto.createHash('sha256').update(content).digest('hex');

  const fileMetadata = {
    filePath,
    fileHash,
    indexingResult: result,
    indexedAt: new Date().toISOString(),
    version: 'langchain-v1',
  };

  // Remove old metadata for this file
  existingMetadata = existingMetadata.filter((meta: any) => meta.filePath !== filePath);
  existingMetadata.push(fileMetadata);

  await fs.writeFile(metadataPath, JSON.stringify(existingMetadata, null, 2));
  console.log(`[LangChain Worker] ✅ Updated metadata for ${filePath}`);
}

/**
 * Save batch indexing metadata
 */
async function saveBatchMetadata(
  projectId: number,
  filePaths: string[],
  result: any
): Promise<void> {
  const project = sqliteService.getProjectById(projectId);
  if (!project?.dataPath) return;

  const metadataPath = path.join(project.dataPath, 'metadata', 'langchain_batch.json');
  await fs.mkdir(path.dirname(metadataPath), { recursive: true });

  const batchMetadata = {
    filePaths,
    indexingResult: result,
    indexedAt: new Date().toISOString(),
    version: 'langchain-v1',
  };

  await fs.writeFile(metadataPath, JSON.stringify(batchMetadata, null, 2));
  console.log(`[LangChain Worker] ✅ Updated batch metadata for ${filePaths.length} files`);
}

/**
 * Handle reindexing (delete old data and reindex)
 */
async function reindexFile(payload: IndexingPayload): Promise<void> {
  const { filePath, projectId, config } = payload;
  
  try {
    console.log(`[LangChain Worker] Reindexing: ${filePath} for project ${projectId}`);

    // Update RAG service config if provided
    if (config) {
      ragService.updateConfig(config);
    }

    parentPort?.postMessage({
      status: 'progress',
      file: filePath,
      progress: 10,
    } as WorkerMessage);

    // Reindex using RAG service
    const indexingResult = await ragService.reindexFile(projectId, filePath);

    parentPort?.postMessage({
      status: 'progress',
      file: filePath,
      progress: 80,
    } as WorkerMessage);

    // Save metadata
    await saveIndexingMetadata(projectId, filePath, indexingResult);

    parentPort?.postMessage({
      status: 'completed',
      file: filePath,
      progress: 100,
      result: indexingResult,
    } as WorkerMessage);

    console.log(`[LangChain Worker] ✅ Completed reindexing ${filePath}:`, indexingResult);

  } catch (error) {
    console.error(`[LangChain Worker] ❌ Error reindexing ${filePath}:`, error);
    parentPort?.postMessage({
      status: 'error',
      file: filePath,
      error: error instanceof Error ? error.message : String(error),
    } as WorkerMessage);
  }
}

// Message handler
parentPort?.on('message', (message: any) => {
  const { type, ...payload } = message;

  switch (type) {
    case 'index_file':
      processFile(payload as IndexingPayload).catch(console.error);
      break;

    case 'index_batch':
      processBatch(payload as IndexingBatchPayload).catch(console.error);
      break;

    case 'reindex_file':
      reindexFile(payload as IndexingPayload).catch(console.error);
      break;

    case 'update_config':
      ragService.updateConfig(payload.config);
      parentPort?.postMessage({ status: 'config_updated' });
      break;

    case 'cancel_indexing':
      console.log('[LangChain Worker] Cancellation requested');
      isCancelled = true;
      parentPort?.postMessage({
        status: 'cancelled' as const,
        error: 'Indexing was cancelled by user'
      } as WorkerMessage);
      break;

    default:
      console.warn(`[LangChain Worker] Unknown message type: ${type}`);
  }
});

// Cleanup on exit
process.on('exit', () => {
  ragService.dispose();
});

console.log('[LangChain Worker] LangChain indexer worker started and ready.');


---

// FILE: src\main\services\langchain-llm.ts

/**
 * LangChain-integrated LLM Service
 * Provides embeddings and chat completions using LangChain abstractions
 */

import { Embeddings } from '@langchain/core/embeddings';
import { BaseLLM } from '@langchain/core/language_models/llms';
import { ChatPromptTemplate } from '@langchain/core/prompts';
import { StringOutputParser } from '@langchain/core/output_parsers';
import { RunnableSequence } from '@langchain/core/runnables';
import { Document } from '@langchain/core/documents';

// Custom Ollama Embeddings implementation
class OllamaEmbeddings extends Embeddings {
  private model: string;
  private baseUrl: string;

  constructor(options: { model?: string; baseUrl?: string } = {}) {
    super({});
    this.model = options.model || 'nomic-embed-text';
    this.baseUrl = options.baseUrl || 'http://localhost:11434';
  }

  async embedDocuments(texts: string[]): Promise<number[][]> {
    const embeddings: number[][] = [];
    
    for (const text of texts) {
      try {
        const response = await fetch(`${this.baseUrl}/api/embeddings`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            model: this.model,
            prompt: text,
          }),
        });

        if (!response.ok) {
          throw new Error(`Ollama API error: ${response.statusText}`);
        }

        const data = await response.json();
        embeddings.push(data.embedding);
      } catch (error) {
        console.warn(`⚠️ Failed to generate embedding for text, using dummy:`, error);
        // Fallback to dummy embedding
        embeddings.push(Array(768).fill(0).map(() => Math.random()));
      }
    }

    return embeddings;
  }

  async embedQuery(text: string): Promise<number[]> {
    const embeddings = await this.embedDocuments([text]);
    return embeddings[0];
  }
}

// Custom Ollama LLM implementation
class OllamaLLM extends BaseLLM {
  private model: string;
  private baseUrl: string;

  constructor(options: { model?: string; baseUrl?: string } = {}) {
    super({});
    this.model = options.model || 'llama3:8b';
    this.baseUrl = options.baseUrl || 'http://localhost:11434';
  }

  _llmType(): string {
    return 'ollama';
  }

  async _generate(prompts: string[]): Promise<any> {
    const results = [];
    for (const prompt of prompts) {
      const response = await this._call(prompt);
      results.push({
        text: response,
        generationInfo: {},
      });
    }
    return {
      generations: results.map(r => [r]),
      llmOutput: {},
    };
  }

  async _call(prompt: string): Promise<string> {
    try {
      const response = await fetch(`${this.baseUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: this.model,
          prompt: prompt,
          stream: false,
        }),
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.statusText}`);
      }

      const data = await response.json();
      return data.response;
    } catch (error) {
      console.warn(`⚠️ Failed to generate response, using dummy:`, error);
      return `This is a dummy response for the prompt: "${prompt.substring(0, 100)}..."`;
    }
  }
}

export type LLMProvider = 'ollama' | 'gemini';

export interface LLMConfig {
  provider: LLMProvider;
  apiKey?: string;
  model: string;
  embeddingModel?: string;
  baseUrl?: string;
}

export class LangChainLLMService {
  private config: LLMConfig;
  private _embeddings!: Embeddings;
  private llm!: BaseLLM;
  private ragChain: RunnableSequence | null = null;

  constructor(config: LLMConfig) {
    this.config = config;
    this.initializeModels();
  }

  get embeddings(): Embeddings {
    return this._embeddings;
  }

  private initializeModels(): void {
    switch (this.config.provider) {
      case 'ollama':
        this._embeddings = new OllamaEmbeddings({
          model: this.config.embeddingModel || 'nomic-embed-text',
          baseUrl: this.config.baseUrl,
        });
        this.llm = new OllamaLLM({
          model: this.config.model,
          baseUrl: this.config.baseUrl,
        });
        break;
      
      case 'gemini':
        // TODO: Implement Gemini integration using @langchain/google-genai
        throw new Error('Gemini integration not yet implemented');
      
      default:
        throw new Error(`Unsupported LLM provider: ${this.config.provider}`);
    }

    this.initializeRAGChain();
  }

  private initializeRAGChain(): void {
    // Create a RAG chain using LangChain LCEL (LangChain Expression Language)
    const prompt = ChatPromptTemplate.fromTemplate(`
You are a helpful coding assistant. Use the following context to answer the question.

Context:
{context}

Question: {question}

Answer:`);

    this.ragChain = RunnableSequence.from([
      {
        context: (input: { context: string; question: string }) => input.context,
        question: (input: { context: string; question: string }) => input.question,
      },
      prompt,
      this.llm,
      new StringOutputParser(),
    ]);
  }

  /**
   * Generate embeddings for a single text
   */
  async generateEmbedding(text: string): Promise<number[]> {
    return await this.embeddings.embedQuery(text);
  }

  /**
   * Generate embeddings for multiple texts
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    return await this.embeddings.embedDocuments(texts);
  }

  /**
   * Generate embeddings for LangChain Documents
   */
  async embedDocuments(documents: Document[]): Promise<{ document: Document; embedding: number[] }[]> {
    const texts = documents.map(doc => doc.pageContent);
    const embeddings = await this.generateEmbeddings(texts);
    
    return documents.map((document, index) => ({
      document,
      embedding: embeddings[index],
    }));
  }

  /**
   * Generate a response using the LLM
   */
  async generateResponse(prompt: string): Promise<string> {
    return await this.llm.invoke(prompt);
  }

  /**
   * Generate a RAG response using context and question
   */
  async generateRAGResponse(context: string, question: string): Promise<string> {
    if (!this.ragChain) {
      throw new Error('RAG chain not initialized');
    }

    return await this.ragChain.invoke({ context, question });
  }

  /**
   * Generate RAG response with retrieved documents
   */
  async generateRAGResponseWithDocs(
    retrievedDocs: Document[],
    question: string
  ): Promise<{ answer: string; sources: Document[] }> {
    const context = retrievedDocs
      .map(doc => `File: ${doc.metadata.file}\n${doc.pageContent}`)
      .join('\n\n---\n\n');

    const answer = await this.generateRAGResponse(context, question);

    return {
      answer,
      sources: retrievedDocs,
    };
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<LLMConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.initializeModels();
  }

  /**
   * Get current configuration
   */
  getConfig(): LLMConfig {
    return { ...this.config };
  }

  /**
   * Test connection to the LLM service
   */
  async testConnection(): Promise<boolean> {
    try {
      const testResponse = await this.generateResponse('Hello, this is a test.');
      return testResponse.length > 0;
    } catch (error) {
      console.error('LLM connection test failed:', error);
      return false;
    }
  }

  /**
   * Test embedding generation
   */
  async testEmbedding(): Promise<boolean> {
    try {
      const embedding = await this.generateEmbedding('Test text for embedding');
      return Array.isArray(embedding) && embedding.length > 0;
    } catch (error) {
      console.error('Embedding test failed:', error);
      return false;
    }
  }
}

// Default instance
export const langChainLLMService = new LangChainLLMService({
  provider: 'ollama',
  model: 'llama3:8b',
  embeddingModel: 'nomic-embed-text',
  baseUrl: 'http://localhost:11434',
});


---

// FILE: src\main\services\langchain-rag.ts

/**
 * LangChain-integrated RAG Service
 * Uses token-oriented chunking with LangChain RecursiveCharacterTextSplitter
 */

import { Document } from '@langchain/core/documents';
import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import fs from 'fs/promises';
import path from 'path';
import { createTokenSplitter, TokenSplitterOptions, getTokenCount, optimizeChunks, validateChunkIntegrity } from './langchain-splitter';
import { LangChainLLMService, LLMConfig } from './langchain-llm';
import { LangChainLanceDBVectorStore } from './langchain-vectorstore';
import { sqliteService } from './sqlite';

export interface RAGConfig {
  llm: LLMConfig;
  chunking: TokenSplitterOptions;
  retrieval: {
    topK: number;
    scoreThreshold?: number;
  };
}

export type IndexingProgressCallback = (progress: {
  completed: number;
  total: number;
  currentFile: string;
}) => void;

export interface IndexingResult {
  totalFiles: number;
  totalChunks: number;
  totalTokens: number;
  chunkingStats: {
    averageSize: number;
    languageDistribution: Record<string, number>;
    typeDistribution: Record<string, number>;
  };
  indexingTime: number;
}

export interface RAGResponse {
  answer: string;
  sources: Document[];
  retrievalStats: {
    totalRetrieved: number;
    averageScore: number;
    topScore: number;
  };
}

export class LangChainRAGService {
  private splitter: RecursiveCharacterTextSplitter;
  private llmService: LangChainLLMService;
  private vectorStores: Map<number, LangChainLanceDBVectorStore> = new Map();
  private config: RAGConfig;

  constructor(config: RAGConfig) {
    this.config = config;
    this.splitter = createTokenSplitter(config.chunking);
    // LLM service is optional for chunking-only operations
    try {
      this.llmService = new LangChainLLMService(config.llm);
    } catch (error) {
      console.warn('[LangChainRAGService] LLM service initialization failed, continuing without LLM:', error);
      this.llmService = null as any; // We'll handle null checks where needed
    }
  }

  /**
   * Save selected files list to JSON file
   */
  private async saveSelectedFiles(projectId: number, filePaths: string[]): Promise<void> {
    try {
      const project = sqliteService.getProjectById(projectId);
      if (!project) {
        throw new Error(`Project ${projectId} not found`);
      }

      const selectedFilesData = filePaths.map(filePath => ({
        path: filePath,
        addedAt: new Date().toISOString(),
      }));

      const selectedFilesPath = path.join(project.dataPath, 'selected_files', 'files.json');
      await fs.mkdir(path.dirname(selectedFilesPath), { recursive: true });
      // Remove formatting for faster writes
      await fs.writeFile(selectedFilesPath, JSON.stringify(selectedFilesData), 'utf-8');

      console.log(`💾 Saved ${filePaths.length} selected files to ${selectedFilesPath}`);
    } catch (error) {
      console.error('❌ Error saving selected files:', error);
      throw error;
    }
  }

  /**
   * Bulk save chunks for multiple files (optimized for performance)
   */
  private async bulkSaveChunks(
    projectId: number,
    allChunksData: Array<{
      filePath: string;
      chunks: Array<{
        id: string;
        content: string;
        metadata: any;
        tokenCount: number;
        charCount: number;
      }>;
    }>
  ): Promise<void> {
    try {
      const chunksDir = await this.ensureChunksDirectory(projectId);
      const project = sqliteService.getProjectById(projectId);
      if (!project) {
        throw new Error(`Project ${projectId} not found`);
      }

      // Write all files in parallel with readable formatting
      const writePromises = allChunksData.map(async ({ filePath, chunks }) => {
        const relativePath = path.relative(project.path, filePath);
        const chunksFileName = `${relativePath.replace(/[\/\\]/g, '_')}.json`;
        const chunksFilePath = path.join(chunksDir, chunksFileName);

        // Use formatted JSON for readability (small performance cost but better UX)
        await fs.writeFile(chunksFilePath, JSON.stringify(chunks, null, 2), 'utf-8');
        return chunks.length;
      });

      const chunkCounts = await Promise.all(writePromises);
      const totalChunks = chunkCounts.reduce((sum, count) => sum + count, 0);

      console.log(`💾 Bulk saved ${totalChunks} chunks for ${allChunksData.length} files`);
    } catch (error) {
      console.error(`❌ Error bulk saving chunks:`, error);
      throw error;
    }
  }

  /**
   * Save chunks for a single source file to a dedicated JSON file.
   * @deprecated Use bulkSaveChunks for better performance
   */
  private async saveChunksForFile(projectId: number, sourceFilePath: string, chunks: Document[]): Promise<void> {
    try {
      const project = sqliteService.getProjectById(projectId);
      if (!project) {
        throw new Error(`Project ${projectId} not found`);
      }

      // Create a sanitized file name from the source path relative to the project root
      const relativePath = path.relative(project.path, sourceFilePath);
      const chunksFileName = `${relativePath.replace(/[\/\\]/g, '_')}.json`;
      const chunksFilePath = path.join(project.dataPath, 'chunks', chunksFileName);

      const chunksData = chunks.map((chunk, index) => ({
        id: `${path.basename(sourceFilePath)}-${index + 1}`,
        content: chunk.pageContent,
        metadata: chunk.metadata,
        tokenCount: getTokenCount(chunk.pageContent),
        charCount: chunk.pageContent.length,
      }));

      await fs.mkdir(path.dirname(chunksFilePath), { recursive: true });
      await fs.writeFile(chunksFilePath, JSON.stringify(chunksData), 'utf-8');

      console.log(`💾 Saved ${chunks.length} chunks for ${sourceFilePath} to ${chunksFilePath}`);
    } catch (error) {
      console.error(`❌ Error saving chunks for file ${sourceFilePath}:`, error);
      throw error;
    }
  }

  /**
   * Optimized: Just ensure chunks directory exists (don't clear unless necessary)
   */
  private async ensureChunksDirectory(projectId: number): Promise<string> {
    try {
      const project = sqliteService.getProjectById(projectId);
      if (!project) {
        throw new Error(`Project ${projectId} not found`);
      }

      const chunksDirPath = path.join(project.dataPath, 'chunks');
      await fs.mkdir(chunksDirPath, { recursive: true });
      return chunksDirPath;
    } catch (error) {
      console.error('❌ Error ensuring chunks directory:', error);
      throw error;
    }
  }

  /**
   * Public method to clear chunks for a project (for manual clearing)
   */
  async clearProjectChunks(projectId: number): Promise<void> {
    await this.clearChunksDirectory(projectId);
  }

  /**
   * Clear existing chunks directory for a project (only when needed)
   */
  private async clearChunksDirectory(projectId: number): Promise<void> {
    try {
      const project = sqliteService.getProjectById(projectId);
      if (!project) {
        console.warn(`[LangChainRAGService] Project ${projectId} not found for clearing chunks.`);
        return;
      }

      const chunksDirPath = path.join(project.dataPath, 'chunks');
      if (await fs.access(chunksDirPath).then(() => true).catch(() => false)) {
        await fs.rm(chunksDirPath, { recursive: true, force: true });
        console.log(`🗑️ Cleared existing chunks directory at ${chunksDirPath}`);
      }
      await fs.mkdir(chunksDirPath, { recursive: true });
    } catch (error) {
      console.error('❌ Error clearing chunks directory:', error);
    }
  }

  /**
   * Get or create vector store for a project
   */
  private async getVectorStore(projectId: number): Promise<LangChainLanceDBVectorStore> {
    if (this.vectorStores.has(projectId)) {
      return this.vectorStores.get(projectId)!;
    }

    const vectorStore = new LangChainLanceDBVectorStore(
      this.llmService.embeddings,
      { projectId }
    );
    
    await vectorStore.initialize();
    this.vectorStores.set(projectId, vectorStore);
    
    return vectorStore;
  }

  /**
   * Index files for a project with optimized batch processing
   */
  async indexFiles(
    projectId: number,
    filePaths: string[],
    onProgress?: IndexingProgressCallback,
    cancellationCheck?: () => boolean,
    clearExisting: boolean = true
  ): Promise<IndexingResult> {
    const startTime = Date.now();
    console.log(`🚀 Starting optimized indexing for project ${projectId}: ${filePaths.length} files`);

    // Clear directory if requested (default behavior for full reindexing)
    if (clearExisting) {
      await this.clearChunksDirectory(projectId);
    } else {
      await this.ensureChunksDirectory(projectId);
    }

    let totalChunks = 0;
    let totalTokens = 0;
    let completedCount = 0;
    const totalFiles = filePaths.length;
    const concurrency = 8; // Increased concurrency for better performance

    // Batch all chunks for bulk write
    const allChunksData: Array<{
      filePath: string;
      chunks: Array<{
        id: string;
        content: string;
        metadata: any;
        tokenCount: number;
        charCount: number;
      }>;
    }> = [];

    // Process files in batches for better performance and memory management
    const processBatch = async (batch: string[]): Promise<{ chunks: number; tokens: number }> => {
      const batchPromises = batch.map(async (filePath) => {
        // Check for cancellation before processing each file
        if (cancellationCheck && cancellationCheck()) {
          console.log(`⏹️ Indexing cancelled, skipping file: ${filePath}`);
          return { chunks: 0, tokens: 0, chunksData: null };
        }

        try {
          let content: string;
          try {
            content = await fs.readFile(filePath, 'utf-8');
          } catch (readError) {
            const buffer = await fs.readFile(filePath);
            content = buffer.toString('utf-8', 0, Math.min(buffer.length, 10000));
          }

          const doc = new Document({
            pageContent: content,
            metadata: {
              source: filePath,
              file: filePath,
              projectId
            },
          });

          // Разбиваем документ на чанки
          const initialChunks = await this.splitter.splitDocuments([doc]);

          // Извлекаем текстовое содержимое для оптимизации
          const chunkTexts = initialChunks.map(chunk => chunk.pageContent);

          // Оптимизируем чанки для лучшего использования токенов
          const optimizedTexts = optimizeChunks(chunkTexts, this.config.chunking.chunkSize);

          // Фильтруем пустые чанки
          const validTexts = optimizedTexts.filter(text => text && text.trim().length > 0);

          // Логируем оптимизацию если произошло объединение
          if (optimizedTexts.length !== chunkTexts.length) {
            console.log(`📦 Chunk optimization: ${chunkTexts.length} → ${optimizedTexts.length} chunks for ${path.basename(filePath)}`);
          }

          // Логируем фильтрацию пустых чанков
          if (validTexts.length !== optimizedTexts.length) {
            console.log(`🧹 Filtered out ${optimizedTexts.length - validTexts.length} empty chunks for ${path.basename(filePath)}`);
          }

          // Если нет валидных чанков, пропускаем файл
          if (validTexts.length === 0) {
            console.warn(`⚠️ No valid chunks found for ${filePath}, skipping`);
            return {
              chunks: 0,
              tokens: 0,
              files: 0
            };
          }

          // Проверяем целостность чанков
          const integrity = validateChunkIntegrity(content, validTexts);
          if (!integrity.isValid) {
            console.warn(`⚠️ Chunk integrity issue in ${filePath}:`, integrity.missingContent);
          }

          // Prepare chunks data for batch write (no individual file writes)
          const chunksData = validTexts.map((chunkText, index) => ({
            id: `${path.basename(filePath)}-${index + 1}`,
            content: chunkText,
            metadata: {
              source: filePath,
              file: filePath,
              projectId,
              loc: {
                lines: {
                  from: 1, // TODO: Можно улучшить, отслеживая реальные номера строк
                  to: content.split('\n').length
                }
              }
            },
            tokenCount: getTokenCount(chunkText),
            charCount: chunkText.length,
          }));

          const fileTokens = chunksData.reduce((sum, chunk) => sum + chunk.tokenCount, 0);

          return {
            chunks: chunksData.length,
            tokens: fileTokens,
            chunksData: { filePath, chunks: chunksData }
          };

        } catch (error) {
          console.warn(`⚠️ Could not process file ${filePath}, skipping:`, error);
          return { chunks: 0, tokens: 0, chunksData: null };
        } finally {
          completedCount++;
          if (onProgress) {
            onProgress({ completed: completedCount, total: totalFiles, currentFile: filePath });
          }
        }
      });

      const results = await Promise.allSettled(batchPromises);
      return results.reduce((acc, result) => {
        if (result.status === 'fulfilled') {
          acc.chunks += result.value.chunks;
          acc.tokens += result.value.tokens;
          if (result.value.chunksData) {
            allChunksData.push(result.value.chunksData);
          }
        }
        return acc;
      }, { chunks: 0, tokens: 0 });
    };

    // Process files in batches
    for (let i = 0; i < filePaths.length; i += concurrency) {
      // Check for cancellation before each batch
      if (cancellationCheck && cancellationCheck()) {
        console.log(`⏹️ Indexing cancelled at batch ${Math.floor(i / concurrency) + 1}`);
        break;
      }

      const batch = filePaths.slice(i, i + concurrency);
      const batchResult = await processBatch(batch);
      totalChunks += batchResult.chunks;
      totalTokens += batchResult.tokens;
    }

    // Bulk write all chunks at once (major performance improvement)
    if (allChunksData.length > 0) {
      await this.bulkSaveChunks(projectId, allChunksData);
    }

    await this.saveSelectedFiles(projectId, filePaths);

    const indexingTime = Date.now() - startTime;

    const result: IndexingResult = {
      totalFiles: filePaths.length,
      totalChunks: totalChunks,
      totalTokens: totalTokens,
      chunkingStats: {
        averageSize: totalChunks > 0 ? Math.round(totalTokens / totalChunks) : 0,
        languageDistribution: {},
        typeDistribution: {},
      },
      indexingTime,
    };

    console.log(`✅ Indexing completed in ${indexingTime}ms:`, result);
    return result;
  }

  /**
   * Index a single file
   */
  async indexFile(projectId: number, filePath: string): Promise<IndexingResult> {
    return await this.indexFiles(projectId, [filePath]);
  }

  /**
   * Re-index a file (delete old chunks and add new ones)
   */
  async reindexFile(projectId: number, filePath: string): Promise<IndexingResult> {
    const vectorStore = await this.getVectorStore(projectId);
    
    await vectorStore.deleteDocumentsByFile(filePath);
    
    return await this.indexFile(projectId, filePath);
  }

  /**
   * Perform RAG query
   */
  async query(
    projectId: number,
    question: string,
    options?: {
      topK?: number;
      scoreThreshold?: number;
      fileFilter?: string;
      languageFilter?: string;
    }
  ): Promise<RAGResponse> {
    console.log(`🔍 RAG query for project ${projectId}: "${question}"`);

    const vectorStore = await this.getVectorStore(projectId);
    const topK = options?.topK || this.config.retrieval.topK;

    const filter: Record<string, any> = {};
    if (options?.fileFilter) filter.file = options.fileFilter;
    if (options?.languageFilter) filter.language = options.languageFilter;

    const retrievedDocsWithScores = await vectorStore.similaritySearchWithScore(
      question,
      topK,
      Object.keys(filter).length > 0 ? filter : undefined
    );

    const scoreThreshold = options?.scoreThreshold || this.config.retrieval.scoreThreshold || 0;
    const filteredDocs = retrievedDocsWithScores.filter(([_, score]) => score >= scoreThreshold);

    if (filteredDocs.length === 0) {
      console.log('⚠️ No relevant documents found');
      return {
        answer: 'I could not find relevant information to answer your question.',
        sources: [],
        retrievalStats: {
          totalRetrieved: 0,
          averageScore: 0,
          topScore: 0,
        },
      };
    }

    const retrievedDocs = filteredDocs.map(([doc]) => doc);
    const scores = filteredDocs.map(([_, score]) => score);

    const ragResponse = await this.llmService.generateRAGResponseWithDocs(
      retrievedDocs,
      question
    );

    const retrievalStats = {
      totalRetrieved: retrievedDocs.length,
      averageScore: scores.reduce((sum, score) => sum + score, 0) / scores.length,
      topScore: Math.max(...scores),
    };

    console.log(`✅ RAG response generated with ${retrievedDocs.length} sources`);

    return {
      answer: ragResponse.answer,
      sources: ragResponse.sources,
      retrievalStats,
    };
  }

  /**
   * Get project statistics (from JSON chunks file)
   */
  async getProjectStats(projectId: number): Promise<{
    vectorStore: { totalDocuments: number; totalTokens: number; totalChars: number };
    llmConnection: boolean;
    embeddingConnection: boolean;
  }> {
    const project = sqliteService.getProjectById(projectId);
    if (!project) {
      throw new Error(`Project ${projectId} not found`);
    }

    const chunksDirPath = path.join(project.dataPath, 'chunks');
    let vectorStoreStats = { totalDocuments: 0, totalTokens: 0, totalChars: 0 };

    try {
      if (await fs.access(chunksDirPath).then(() => true).catch(() => false)) {
        const chunkFiles = await fs.readdir(chunksDirPath);
        for (const file of chunkFiles) {
          if (file.endsWith('.json')) {
            const chunksData = JSON.parse(await fs.readFile(path.join(chunksDirPath, file), 'utf-8'));
            vectorStoreStats.totalDocuments += chunksData.length;
            vectorStoreStats.totalTokens += chunksData.reduce((sum: number, chunk: any) => sum + (chunk.tokenCount || 0), 0);
            vectorStoreStats.totalChars += chunksData.reduce((sum: number, chunk: any) => sum + (chunk.charCount || 0), 0);
          }
        }
      }
    } catch (error) {
      console.warn('⚠️ Could not read chunks directory for stats:', error);
    }

    const llmConnection = await this.llmService.testConnection();
    const embeddingConnection = await this.llmService.testEmbedding();

    return {
      vectorStore: vectorStoreStats,
      llmConnection,
      embeddingConnection,
    };
  }

  /**
   * Search documents by metadata
   */
  async searchByMetadata(
    projectId: number,
    query: string,
    metadata: { file?: string; language?: string; type?: string },
    topK: number = 10
  ): Promise<Document[]> {
    const vectorStore = await this.getVectorStore(projectId);
    return await vectorStore.searchByMetadata(query, metadata, topK);
  }

  /**
   * Get all documents for a specific file
   */
  async getFileDocuments(projectId: number, filePath: string): Promise<Document[]> {
    const vectorStore = await this.getVectorStore(projectId);
    return await vectorStore.getDocumentsByFile(filePath);
  }

  /**
   * Delete all data for a project
   */
  async deleteProject(projectId: number): Promise<void> {
    if (this.vectorStores.has(projectId)) {
      const vectorStore = this.vectorStores.get(projectId)!;
      await vectorStore.close();
      this.vectorStores.delete(projectId);
    }
    
    console.log(`✅ Cleaned up RAG data for project ${projectId}`);
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<RAGConfig>): void {
    this.config = { ...this.config, ...newConfig };

    if (newConfig.llm) {
      this.llmService.updateConfig(newConfig.llm);
    }

    if (newConfig.chunking) {
      this.splitter = createTokenSplitter({
        ...this.config.chunking,
        ...newConfig.chunking,
      });
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): RAGConfig {
    return { ...this.config };
  }

  /**
   * Test all connections
   */
  async testConnections(): Promise<{
    llm: boolean;
    embedding: boolean;
    splitter: boolean;
  }> {
    const llm = await this.llmService.testConnection();
    const embedding = await this.llmService.testEmbedding();

    let splitter = true;
    try {
      await this.splitter.splitText('This is a test text for the token splitter.');
    } catch (error) {
      splitter = false;
    }

    return { llm, embedding, splitter };
  }

  /**
   * Cleanup resources
   */
  dispose(): void {
    this.vectorStores.clear();
  }
}

export const ragService = new LangChainRAGService({
  llm: {
    provider: 'ollama',
    model: 'llama3:8b',
    embeddingModel: 'nomic-embed-text',
    baseUrl: 'http://localhost:11434',
  },
  chunking: {
    chunkSize: 512,         // 2025 best practice: 512 tokens
    chunkOverlap: 102,      // 2025 best practice: 20% overlap (512 * 0.2 = 102)
  },
  retrieval: {
    topK: 10,
    scoreThreshold: 0.1,
  },
});


---

// FILE: src\main\services\langchain-splitter.ts

/**
 * LangChain Token-based Text Splitter
 * Replaces tree-sitter chunking with standard token-oriented splitting
 * Uses js-tiktoken for OFFLINE token counting
 */

import { RecursiveCharacterTextSplitter } from 'langchain/text_splitter';
import { getEncoding } from 'js-tiktoken';

export interface TokenSplitterOptions {
  chunkSize: number;      // Размер чанка в ТОКЕНАХ (приблизительно)
  chunkOverlap: number;   // Пересечение в ТОКЕНАХ (приблизительно)
  encodingName?: "cl100k_base" | "p50k_base" | "r50k_base"; // Тип кодировки (для совместимости)
}

/**
 * Custom Token-aware Text Splitter using js-tiktoken for OFFLINE operation
 * Создает RecursiveCharacterTextSplitter с точным подсчетом токенов
 */
export function createTokenSplitter(options: TokenSplitterOptions): RecursiveCharacterTextSplitter {
  // Создаем энкодер js-tiktoken для точного подсчета токенов
  const encoding = getEncoding(options.encodingName || 'cl100k_base');

  // Функция для подсчета токенов
  const lengthFunction = (text: string): number => {
    return encoding.encode(text).length;
  };

  return new RecursiveCharacterTextSplitter({
    chunkSize: options.chunkSize,
    chunkOverlap: options.chunkOverlap,
    lengthFunction: lengthFunction,
    // Улучшенные сепараторы для лучшего разбиения кода
    separators: [
      "\n\n\n",           // Тройные переносы (между большими блоками)
      "\n\n",             // Двойные переносы (между функциями/классами)
      "\n\n  ",           // Отступы с двойными переносами
      "\n},\n",           // Конец объектов/интерфейсов
      "\n};\n",           // Конец функций/классов
      "\n}\n",            // Конец блоков
      "\n",               // Одинарные переносы
      ". ",               // Конец предложений
      ", ",               // Запятые
      " ",                // Пробелы
      ""                  // Символы (последний резерв)
    ],
    // Сохранять сепараторы для лучшего контекста
    keepSeparator: true,
  });
}

// Cache encoding for performance (major optimization)
let cachedEncoding: any = null;
let cachedEncodingName: string = '';

/**
 * Helper function to get token count for a text using js-tiktoken OFFLINE
 * Uses cached encoding for massive performance improvement
 */
export function getTokenCount(text: string, encodingName: string = 'cl100k_base'): number {
  // Use cached encoding if same encoding name
  if (cachedEncoding && cachedEncodingName === encodingName) {
    const tokens = cachedEncoding.encode(text);
    return tokens.length;
  }

  // Create and cache new encoding
  cachedEncoding = getEncoding(encodingName as any);
  cachedEncodingName = encodingName;
  const tokens = cachedEncoding.encode(text);
  return tokens.length;
}

/**
 * Интеллектуальное объединение маленьких чанков для оптимального использования токенов
 * Объединяет соседние чанки, если их общий размер не превышает maxTokens
 * Updated for 2025 best practices: default 512 tokens
 */
export function optimizeChunks(chunks: string[], maxTokens: number = 512, encodingName: string = 'cl100k_base'): string[] {
  if (chunks.length <= 1) return chunks;

  const optimizedChunks: string[] = [];
  let currentChunk = chunks[0];
  let currentTokens = getTokenCount(currentChunk, encodingName);

  for (let i = 1; i < chunks.length; i++) {
    const nextChunk = chunks[i];
    const nextTokens = getTokenCount(nextChunk, encodingName);

    // Проверяем, можем ли объединить чанки
    const combinedTokens = getTokenCount(currentChunk + '\n\n' + nextChunk, encodingName);

    if (combinedTokens <= maxTokens) {
      // Объединяем чанки с разделителем
      currentChunk = currentChunk + '\n\n' + nextChunk;
      currentTokens = combinedTokens;
    } else {
      // Сохраняем текущий чанк и начинаем новый
      optimizedChunks.push(currentChunk);
      currentChunk = nextChunk;
      currentTokens = nextTokens;
    }
  }

  // Добавляем последний чанк
  optimizedChunks.push(currentChunk);

  return optimizedChunks;
}

/**
 * Проверяет целостность чанков - нет ли пропусков в тексте
 */
export function validateChunkIntegrity(originalText: string, chunks: string[]): { isValid: boolean; missingContent?: string } {
  const combinedChunks = chunks.join('');
  const originalNormalized = originalText.replace(/\s+/g, ' ').trim();
  const combinedNormalized = combinedChunks.replace(/\s+/g, ' ').trim();

  if (originalNormalized === combinedNormalized) {
    return { isValid: true };
  }

  // Найти пропущенный контент
  const missingContent = originalNormalized.replace(combinedNormalized, '').trim();
  return {
    isValid: false,
    missingContent: missingContent.length > 100 ? missingContent.substring(0, 100) + '...' : missingContent
  };
}


---

// FILE: src\main\services\mcp_api.ts

/**
 * @file src/main/services/mcp_api.ts
 * @description Local MCP-compliant API server using Express.
 * Provides context to AI coding tools.
 */

import express from 'express';

const PORT = 4888; // Port for the local MCP server

class MCPApiService {
  private app: express.Application;
  private server: any;

  constructor() {
    this.app = express();
    this.app.use(express.json());
    this.setupRoutes();
  }

  private setupRoutes() {
    this.app.get('/mcp/v1/status', (req, res) => {
      res.json({ status: 'running', timestamp: new Date().toISOString() });
    });

    this.app.post('/mcp/v1/context', async (req, res) => {
      const { question } = req.body;
      if (!question) {
        return res.status(400).json({ error: 'Missing "question" in request body' });
      }

      try {
        // Simplified response - RAG services have been removed
        res.json({
          context: `MCP API is running but RAG services have been removed. Question: ${question}`,
          sources: [],
          message: 'RAG functionality has been removed from this version'
        });
      } catch (error) {
        console.error('[MCP API] Error generating context:', error);
        res.status(500).json({
          error: 'Failed to generate context',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Legacy endpoints - functionality removed
    this.app.post('/mcp/v1/langchain/query', async (req, res) => {
      res.status(410).json({
        success: false,
        error: 'LangChain functionality has been removed',
        message: 'This endpoint is no longer available'
      });
    });

    this.app.get('/mcp/v1/langchain/stats/:projectId', async (req, res) => {
      res.status(410).json({
        success: false,
        error: 'LangChain functionality has been removed',
        message: 'This endpoint is no longer available'
      });
    });
  }

  start() {
    if (this.server) {
      console.log('MCP API server is already running.');
      return;
    }
    this.server = this.app.listen(PORT, '127.0.0.1', () => {
      console.log(`MCP API server started at http://127.0.0.1:${PORT}`);
    });
  }

  stop() {
    if (this.server) {
      this.server.close(() => {
        console.log('MCP API server stopped.');
        this.server = null;
      });
    }
  }
}

export const mcpApiService = new MCPApiService();


---

// FILE: src\main\services\ollama-api.ts

/**
 * @file src/main/services/ollama-api.ts
 * @description Service for interacting with Ollama API to fetch available models.
 */

export interface OllamaModel {
  name: string;
  model: string;
  modified_at: string;
  size: number;
  digest: string;
  details?: {
    parent_model?: string;
    format?: string;
    family?: string;
    families?: string[];
    parameter_size?: string;
    quantization_level?: string;
  };
}

export interface OllamaModelsResponse {
  models: OllamaModel[];
}

export class OllamaAPIService {
  private baseUrl: string;

  constructor(baseUrl: string = 'http://localhost:11434') {
    this.baseUrl = baseUrl;
  }

  /**
   * Fetch all available models from Ollama
   */
  async getAvailableModels(): Promise<OllamaModel[]> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
      }

      const data: OllamaModelsResponse = await response.json();
      return data.models || [];
    } catch (error) {
      console.error('Failed to fetch Ollama models:', error);
      throw error;
    }
  }

  /**
   * Get embedding models specifically
   */
  async getEmbeddingModels(): Promise<string[]> {
    try {
      const allModels = await this.getAvailableModels();

      // Filter models that are suitable for embeddings
      const embeddingModels = allModels
        .map(model => model.name)
        .filter(name => this.isEmbeddingModel(name));

      return embeddingModels;
    } catch (error) {
      console.error('[OllamaAPIService] Error getting embedding models:', error);
      return [];
    }
  }

  /**
   * Get LLM models specifically
   */
  async getLLMModels(): Promise<string[]> {
    try {
      const allModels = await this.getAvailableModels();

      // Filter models that are suitable for text generation
      const llmModels = allModels
        .map(model => model.name)
        .filter(name => !this.isEmbeddingModel(name));

      return llmModels;
    } catch (error) {
      console.error('[OllamaAPIService] Error getting LLM models:', error);
      return [];
    }
  }

  /**
   * Check if Ollama server is running
   */
  async isServerRunning(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/tags`, {
        method: 'GET',
        signal: AbortSignal.timeout(5000), // 5 second timeout
      });
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * Check if a model name is likely an embedding model
   */
  private isEmbeddingModel(modelName: string): boolean {
    const embeddingKeywords = [
      'embed', 'embedding', 'nomic-embed', 'mxbai-embed',
      'all-minilm', 'bge-', 'snowflake-arctic-embed',
      'text-embedding', 'sentence-transformer',
      'qwen3-embedding', 'qwen-embedding'
    ];

    const lowerName = modelName.toLowerCase();
    return embeddingKeywords.some(keyword => lowerName.includes(keyword));
  }


}

// Default instance
export const ollamaAPIService = new OllamaAPIService();


---

// FILE: src\main\services\project-files.ts

/**
 * @file src/main/services/project-files.ts
 * @description Service for managing project files - copying, linking, and organizing selected files
 */

import fs from 'fs/promises';
import fsSync from 'fs';
import path from 'path';
import crypto from 'crypto';
import { sqliteService } from './sqlite';

export interface ProjectFile {
  id: string;
  originalPath: string;
  storedPath: string;
  fileName: string;
  fileHash: string;
  size: number;
  addedAt: string;
  projectId: number;
}

export interface FileSelectionInfo {
  filePaths: string[];
  projectId: number;
  copyFiles?: boolean; // true = copy files, false = create symlinks
}

class ProjectFilesService {
  
  /**
   * Add selected files to project storage
   */
  async addSelectedFiles(selection: FileSelectionInfo): Promise<ProjectFile[]> {
    const project = sqliteService.getProjectById(selection.projectId);
    if (!project?.dataPath) {
      throw new Error(`Project with ID ${selection.projectId} not found`);
    }

    const selectedFilesPath = path.join(project.dataPath, 'selected_files');
    const results: ProjectFile[] = [];

    for (const filePath of selection.filePaths) {
      try {
        const result = await this.addSingleFile(filePath, selectedFilesPath, selection.projectId, selection.copyFiles);
        results.push(result);
      } catch (error) {
        console.error(`❌ Failed to add file ${filePath}:`, error);
      }
    }

    // Save selection metadata
    await this.saveSelectionMetadata(project.dataPath, results);
    
    console.log(`✅ Added ${results.length} files to project ${selection.projectId}`);
    return results;
  }

  private async addSingleFile(
    originalPath: string,
    selectedFilesPath: string,
    projectId: number,
    copyFiles: boolean = false  // Changed default to false - store paths only
  ): Promise<ProjectFile> {
    // Check if file exists
    const stats = await fs.stat(originalPath);
    if (!stats.isFile()) {
      throw new Error(`Path is not a file: ${originalPath}`);
    }

    // Generate file hash
    const fileBuffer = await fs.readFile(originalPath);
    const fileHash = crypto.createHash('sha256').update(fileBuffer).digest('hex');
    
    // Generate unique ID
    const fileId = crypto.randomUUID();
    const fileName = path.basename(originalPath);
    const fileExtension = path.extname(fileName);
    const baseName = path.basename(fileName, fileExtension);
    
    let storedPath: string;

    if (copyFiles) {
      // Create unique stored filename to avoid conflicts
      const storedFileName = `${baseName}_${fileId.slice(0, 8)}${fileExtension}`;
      storedPath = path.join(selectedFilesPath, storedFileName);

      // Ensure directory exists
      await fs.mkdir(selectedFilesPath, { recursive: true });

      // Copy file
      await fs.copyFile(originalPath, storedPath);
      console.log(`📄 Copied: ${originalPath} -> ${storedPath}`);
    } else {
      // Just store the original path - no copying
      storedPath = originalPath;
      console.log(`📝 Referenced: ${originalPath}`);
    }

    return {
      id: fileId,
      originalPath,
      storedPath,
      fileName,
      fileHash,
      size: stats.size,
      addedAt: new Date().toISOString(),
      projectId
    };
  }

  private async saveSelectionMetadata(projectDataPath: string, files: ProjectFile[]): Promise<void> {
    const metadataPath = path.join(projectDataPath, 'metadata', 'selected_files.json');
    await fs.mkdir(path.dirname(metadataPath), { recursive: true });
    
    // Load existing metadata
    let existingFiles: ProjectFile[] = [];
    try {
      const existingData = await fs.readFile(metadataPath, 'utf-8');
      existingFiles = JSON.parse(existingData);
    } catch (error) {
      // File doesn't exist or is invalid, start fresh
    }

    // Merge with new files (avoid duplicates by hash)
    const existingHashes = new Set(existingFiles.map(f => f.fileHash));
    const newFiles = files.filter(f => !existingHashes.has(f.fileHash));
    const allFiles = [...existingFiles, ...newFiles];

    // Save updated metadata
    await fs.writeFile(metadataPath, JSON.stringify(allFiles, null, 2));
    console.log(`💾 Saved metadata for ${allFiles.length} files`);
  }

  /**
   * Get all selected files for a project
   */
  async getSelectedFiles(projectId: number): Promise<ProjectFile[]> {
    const project = sqliteService.getProjectById(projectId);
    if (!project?.dataPath) {
      throw new Error(`Project with ID ${projectId} not found`);
    }

    const metadataPath = path.join(project.dataPath, 'metadata', 'selected_files.json');
    
    try {
      const data = await fs.readFile(metadataPath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      // No files selected yet
      return [];
    }
  }

  /**
   * Remove a file from project selection
   */
  async removeSelectedFile(projectId: number, fileId: string): Promise<void> {
    const project = sqliteService.getProjectById(projectId);
    if (!project?.dataPath) {
      throw new Error(`Project with ID ${projectId} not found`);
    }

    const metadataPath = path.join(project.dataPath, 'metadata', 'selected_files.json');
    
    try {
      const data = await fs.readFile(metadataPath, 'utf-8');
      const files: ProjectFile[] = JSON.parse(data);
      
      const fileToRemove = files.find(f => f.id === fileId);
      if (fileToRemove) {
        // Remove physical file
        try {
          await fs.unlink(fileToRemove.storedPath);
        } catch (error) {
          console.warn(`⚠️ Could not remove file: ${fileToRemove.storedPath}`);
        }
        
        // Update metadata
        const updatedFiles = files.filter(f => f.id !== fileId);
        await fs.writeFile(metadataPath, JSON.stringify(updatedFiles, null, 2));
        
        console.log(`🗑️ Removed file: ${fileToRemove.fileName}`);
      }
    } catch (error) {
      console.error(`❌ Failed to remove file ${fileId}:`, error);
    }
  }

  /**
   * Clear all selected files for a project
   */
  async clearSelectedFiles(projectId: number): Promise<void> {
    const project = sqliteService.getProjectById(projectId);
    if (!project?.dataPath) {
      throw new Error(`Project with ID ${projectId} not found`);
    }

    const selectedFilesPath = path.join(project.dataPath, 'selected_files');
    const metadataPath = path.join(project.dataPath, 'metadata', 'selected_files.json');
    
    try {
      // Remove all files
      if (fsSync.existsSync(selectedFilesPath)) {
        await fs.rm(selectedFilesPath, { recursive: true, force: true });
      }
      
      // Clear metadata
      if (fsSync.existsSync(metadataPath)) {
        await fs.unlink(metadataPath);
      }
      
      console.log(`🧹 Cleared all selected files for project ${projectId}`);
    } catch (error) {
      console.error(`❌ Failed to clear selected files:`, error);
    }
  }

  /**
   * Get file paths for indexing (returns stored paths)
   */
  async getFilePathsForIndexing(projectId: number): Promise<string[]> {
    const files = await this.getSelectedFiles(projectId);
    return files.map(f => f.storedPath);
  }

  /**
   * Validate that all stored files still exist
   */
  async validateStoredFiles(projectId: number): Promise<{ valid: ProjectFile[], missing: ProjectFile[] }> {
    const files = await this.getSelectedFiles(projectId);
    const valid: ProjectFile[] = [];
    const missing: ProjectFile[] = [];

    for (const file of files) {
      try {
        await fs.access(file.storedPath);
        valid.push(file);
      } catch (error) {
        missing.push(file);
      }
    }

    if (missing.length > 0) {
      console.warn(`⚠️ Found ${missing.length} missing files in project ${projectId}`);
    }

    return { valid, missing };
  }
}

export const projectFilesService = new ProjectFilesService();


---

// FILE: src\main\services\simple-chunker.ts

/**
 * @file Smart Semantic Chunker with Modern Token-Oriented Approach
 * @description Combines semantic code analysis with 2025 best practices:
 * - 512 tokens with 20% overlap as universal baseline
 * - Token-oriented approach using js-tiktoken for precision
 * - Structure-aware chunking for code files
 * - Optimized for modern RAG systems
 */

import fs from 'fs/promises';
import path from 'path';
import { countTokens, splitByTokenLimit } from '../utils/tokenizer';

export interface Chunk {
  text: string;
  metadata: {
    file: string;
    startLine: number;
    endLine: number;
    type: string;
    language: string;
    name?: string;
    size: number;
  };
}

export interface ChunkingOptions {
  maxTokens: number;
  overlap: number;
  preserveStructure: boolean;
  minTokens?: number;
}

export class SimpleSmartChunker {
  private maxTokens: number = 2048;  // Современные модели работают с 2048+ токенами
  private overlap: number = 200;     // Разумный overlap для больших чанков
  private preserveStructure: boolean = true;
  private minTokens: number = 100;   // Минимальный размер чанка для сохранения смысла
  private semanticThreshold: number = 3072; // 1.5x maxTokens - порог для принудительного разделения

  constructor(options?: Partial<ChunkingOptions>) {
    if (options) {
      this.maxTokens = options.maxTokens ?? this.maxTokens;
      this.overlap = options.overlap ?? this.overlap;
      this.preserveStructure = options.preserveStructure ?? this.preserveStructure;
      this.minTokens = options.minTokens ?? this.minTokens;
    }

    this.semanticThreshold = this.maxTokens * 1.5;

    // Валидация параметров
    if (this.maxTokens < 500) {
      console.warn('⚠️ maxTokens слишком мал для современных моделей, установлен минимум 500');
      this.maxTokens = 500;
    }
    if (this.overlap >= this.maxTokens / 3) {
      console.warn('⚠️ overlap слишком большой, установлен в 1/5 от maxTokens');
      this.overlap = Math.floor(this.maxTokens / 5);
    }
    if (this.minTokens < 50) {
      console.warn('⚠️ minTokens слишком мал, установлен минимум 50');
      this.minTokens = 50;
    }
  }

  private getLanguageFromExtension(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      '.js': 'javascript',
      '.jsx': 'javascript',
      '.ts': 'typescript',
      '.tsx': 'typescript',
      '.py': 'python',
      '.java': 'java',
      '.cpp': 'cpp',
      '.c': 'c',
      '.h': 'c',
      '.cs': 'csharp',
      '.go': 'go',
      '.rs': 'rust',
      '.php': 'php',
      '.rb': 'ruby'
    };
    return languageMap[ext] || 'text';
  }

  private extractSemanticChunks(content: string, language: string): Array<{
    text: string;
    startLine: number;
    endLine: number;
    type: string;
    name?: string;
  }> {
    const lines = content.split('\n');
    const chunks: Array<{
      text: string;
      startLine: number;
      endLine: number;
      type: string;
      name?: string;
    }> = [];

    let currentChunk = '';
    let startLine = 1;
    let currentLine = 1;
    let currentType = 'text';
    let currentName: string | undefined;

    for (const line of lines) {
      const trimmed = line.trim();
      
      // Detect semantic boundaries
      const boundary = this.detectSemanticBoundary(line, language);
      
      if (boundary) {
        // Save current chunk if it exists
        if (currentChunk.trim()) {
          chunks.push({
            text: currentChunk.trim(),
            startLine,
            endLine: currentLine - 1,
            type: currentType,
            name: currentName
          });
        }
        
        // Start new chunk
        currentChunk = line + '\n';
        startLine = currentLine;
        currentType = boundary.type;
        currentName = boundary.name;
      } else {
        currentChunk += line + '\n';
        
        // Check if chunk is getting too large
        const tokens = countTokens(currentChunk);
        if (tokens >= this.maxTokens) {
          chunks.push({
            text: currentChunk.trim(),
            startLine,
            endLine: currentLine,
            type: currentType,
            name: currentName
          });
          
          currentChunk = '';
          startLine = currentLine + 1;
          currentType = 'text';
          currentName = undefined;
        }
      }
      
      currentLine++;
    }

    // Add remaining chunk
    if (currentChunk.trim()) {
      chunks.push({
        text: currentChunk.trim(),
        startLine,
        endLine: currentLine - 1,
        type: currentType,
        name: currentName
      });
    }

    return chunks;
  }

  private detectSemanticBoundary(line: string, language: string): { type: string; name?: string } | null {
    const trimmed = line.trim();
    
    // JavaScript/TypeScript patterns
    if (language === 'javascript' || language === 'typescript') {
      // Function declarations
      const funcMatch = trimmed.match(/^(?:export\s+)?(?:async\s+)?function\s+([a-zA-Z_][a-zA-Z0-9_]*)/);
      if (funcMatch) return { type: 'function', name: funcMatch[1] };
      
      // Arrow functions
      const arrowMatch = trimmed.match(/^(?:export\s+)?(?:const|let|var)\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*=.*=>/);
      if (arrowMatch) return { type: 'function', name: arrowMatch[1] };
      
      // Class declarations
      const classMatch = trimmed.match(/^(?:export\s+)?class\s+([a-zA-Z_][a-zA-Z0-9_]*)/);
      if (classMatch) return { type: 'class', name: classMatch[1] };
      
      // Interface/type declarations (TypeScript)
      const interfaceMatch = trimmed.match(/^(?:export\s+)?interface\s+([a-zA-Z_][a-zA-Z0-9_]*)/);
      if (interfaceMatch) return { type: 'interface', name: interfaceMatch[1] };
      
      const typeMatch = trimmed.match(/^(?:export\s+)?type\s+([a-zA-Z_][a-zA-Z0-9_]*)/);
      if (typeMatch) return { type: 'type', name: typeMatch[1] };
    }
    
    // Python patterns
    if (language === 'python') {
      // Function definitions
      const funcMatch = trimmed.match(/^def\s+([a-zA-Z_][a-zA-Z0-9_]*)/);
      if (funcMatch) return { type: 'function', name: funcMatch[1] };
      
      // Class definitions
      const classMatch = trimmed.match(/^class\s+([a-zA-Z_][a-zA-Z0-9_]*)/);
      if (classMatch) return { type: 'class', name: classMatch[1] };
    }
    
    // Java patterns
    if (language === 'java') {
      const methodMatch = trimmed.match(/^(?:public|private|protected)?\s*(?:static)?\s*(?:\w+\s+)*(\w+)\s*\(/);
      if (methodMatch && !trimmed.includes('class')) return { type: 'method', name: methodMatch[1] };
      
      const classMatch = trimmed.match(/^(?:public|private)?\s*class\s+([a-zA-Z_][a-zA-Z0-9_]*)/);
      if (classMatch) return { type: 'class', name: classMatch[1] };
    }
    
    // Import statements
    if (trimmed.startsWith('import ') || trimmed.startsWith('from ') || trimmed.startsWith('#include')) {
      return { type: 'import' };
    }
    
    return null;
  }

  async chunkFile(filePath: string): Promise<Chunk[]> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const language = this.getLanguageFromExtension(filePath);
      
      if (!this.preserveStructure) {
        return this.chunkText(content, filePath, language);
      }
      
      const semanticChunks = this.extractSemanticChunks(content, language);
      const chunks: Chunk[] = [];
      
      for (const chunk of semanticChunks) {
        const tokenCount = countTokens(chunk.text);
        
        if (tokenCount <= this.maxTokens) {
          chunks.push({
            text: chunk.text,
            metadata: {
              file: filePath,
              startLine: chunk.startLine,
              endLine: chunk.endLine,
              type: chunk.type,
              language,
              name: chunk.name,
              size: tokenCount
            }
          });
        } else {
          // Современный подход: сохраняем семантическую целостность
          if (chunk.type === 'function' || chunk.type === 'class' || chunk.type === 'interface') {
            // Для семантических единиц применяем более либеральный подход
            if (tokenCount <= this.semanticThreshold) {
              // Функции до 3000+ токенов сохраняем целыми - современные модели справляются
              console.log(`📦 Большая ${chunk.type} "${chunk.name}" (${tokenCount} токенов) сохранена целой для семантической целостности`);
              chunks.push({
                text: chunk.text,
                metadata: {
                  file: filePath,
                  startLine: chunk.startLine,
                  endLine: chunk.endLine,
                  type: `${chunk.type}_intact`, // Помечаем как целостный
                  language,
                  name: chunk.name,
                  size: tokenCount
                }
              });
            } else {
              // Только экстремально большие функции (>3000 токенов) разделяем
              console.log(`🔪 Разделение экстремально большой ${chunk.type} "${chunk.name}" (${tokenCount} токенов)`);

              // Пытаемся разделить по логическим блокам, а не просто по размеру
              const subChunks = this.splitLargeSemanticUnit(chunk.text, chunk.type, language);

              subChunks.forEach((subChunk, index) => {
                const subTokenCount = countTokens(subChunk);
                if (subTokenCount >= this.minTokens) {
                  chunks.push({
                    text: subChunk,
                    metadata: {
                      file: filePath,
                      startLine: chunk.startLine,
                      endLine: chunk.endLine,
                      type: `${chunk.type}_section_${index + 1}`,
                      language,
                      name: chunk.name,
                      size: subTokenCount
                    }
                  });
                }
              });
            }
          } else {
            // Для обычного текста используем стандартное разделение
            const subChunks = splitByTokenLimit(chunk.text, this.maxTokens, this.overlap);
            subChunks.forEach((subChunk, index) => {
              const subTokenCount = countTokens(subChunk);
              if (subTokenCount >= this.minTokens) {
                chunks.push({
                  text: subChunk,
                  metadata: {
                    file: filePath,
                    startLine: chunk.startLine,
                    endLine: chunk.endLine,
                    type: `${chunk.type}_part_${index + 1}`,
                    language,
                    name: chunk.name,
                    size: subTokenCount
                  }
                });
              }
            });
          }
        }
      }
      
      return chunks;
    } catch (error) {
      console.error(`Error chunking file ${filePath}:`, error);
      throw error;
    }
  }

  /**
   * Умное разделение больших семантических единиц (функций, классов)
   * Пытается сохранить логическую структуру вместо простого разделения по размеру
   */
  private splitLargeSemanticUnit(text: string, type: string, language: string): string[] {
    const lines = text.split('\n');
    const chunks: string[] = [];
    let currentChunk = '';
    let braceLevel = 0;
    let inDocstring = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const trimmed = line.trim();

      // Отслеживаем уровень вложенности для разумного разделения
      if (language === 'javascript' || language === 'typescript') {
        // Подсчет фигурных скобок
        braceLevel += (line.match(/\{/g) || []).length;
        braceLevel -= (line.match(/\}/g) || []).length;
      } else if (language === 'python') {
        // Для Python отслеживаем docstrings
        if (trimmed.includes('"""') || trimmed.includes("'''")) {
          inDocstring = !inDocstring;
        }
      }

      currentChunk += line + '\n';

      // Проверяем, можно ли разделить здесь
      const canSplit = this.canSplitHere(line, braceLevel, inDocstring, language);
      const currentTokens = countTokens(currentChunk);

      if (currentTokens >= this.maxTokens && canSplit && currentChunk.trim()) {
        chunks.push(currentChunk.trim());
        currentChunk = '';
      }
    }

    if (currentChunk.trim()) {
      chunks.push(currentChunk.trim());
    }

    // Если не удалось разделить разумно, используем стандартное разделение
    if (chunks.length === 1) {
      return splitByTokenLimit(text, this.maxTokens, this.overlap);
    }

    return chunks;
  }

  /**
   * Определяет, можно ли разделить код в данной точке без потери семантики
   */
  private canSplitHere(line: string, braceLevel: number, inDocstring: boolean, language: string): boolean {
    const trimmed = line.trim();

    // Не разделяем внутри docstrings
    if (inDocstring) return false;

    if (language === 'javascript' || language === 'typescript') {
      // Можно разделить на верхнем уровне после закрывающей скобки
      if (braceLevel === 1 && trimmed === '}') return true;

      // Можно разделить после комментариев
      if (trimmed.startsWith('//') || trimmed.startsWith('/*')) return true;

      // Можно разделить после точки с запятой на верхнем уровне
      if (braceLevel === 1 && trimmed.endsWith(';')) return true;
    } else if (language === 'python') {
      // Можно разделить после функций/классов на том же уровне отступа
      if (trimmed === '' && !inDocstring) return true;

      // Можно разделить после комментариев
      if (trimmed.startsWith('#')) return true;
    }

    return false;
  }

  private async chunkText(content: string, filePath: string, language: string): Promise<Chunk[]> {
    const chunks = splitByTokenLimit(content, this.maxTokens, this.overlap);
    return chunks.map((chunk) => ({
      text: chunk,
      metadata: {
        file: filePath,
        startLine: 1,
        endLine: content.split('\n').length,
        type: 'text',
        language,
        size: countTokens(chunk)
      }
    }));
  }

  async chunkFiles(filePaths: string[]): Promise<Chunk[]> {
    const allChunks: Chunk[] = [];
    
    for (const filePath of filePaths) {
      try {
        const chunks = await this.chunkFile(filePath);
        allChunks.push(...chunks);
        console.log(`✅ Chunked ${filePath}: ${chunks.length} chunks`);
      } catch (error) {
        console.error(`❌ Failed to chunk file ${filePath}:`, error);
      }
    }
    
    return allChunks;
  }
}


---

// FILE: src\main\services\sqlite.ts

/**
 * @file src/main/services/sqlite.ts
 * @description Service for interacting with the SQLite database.
 * Manages projects, metadata, and history.
 */

import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';

const DB_PATH = path.join(process.cwd(), 'database');
if (!fs.existsSync(DB_PATH)) {
  fs.mkdirSync(DB_PATH, { recursive: true });
}

const db = new Database(path.join(DB_PATH, 'smart-rag.db'));

// --- Schema Initialization ---
const initSchema = () => {
  db.exec(`
    CREATE TABLE IF NOT EXISTS projects (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL UNIQUE,
      path TEXT NOT NULL UNIQUE,
      dataPath TEXT NOT NULL,
      createdAt TEXT DEFAULT CURRENT_TIMESTAMP
    );
  `);

  db.exec(`
    CREATE TABLE IF NOT EXISTS documents (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      projectId INTEGER NOT NULL,
      filePath TEXT NOT NULL,
      fileHash TEXT NOT NULL,
      lastIndexedAt TEXT DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (projectId) REFERENCES projects (id) ON DELETE CASCADE,
      UNIQUE (projectId, filePath)
    );
  `);
};

initSchema();

// --- Migration for existing projects ---
const migrateExistingProjects = () => {
  try {
    // Check if dataPath column exists
    const columns = db.prepare("PRAGMA table_info(projects)").all() as any[];
    const hasDataPath = columns.some(col => col.name === 'dataPath');

    if (!hasDataPath) {
      console.log('[SQLite] Adding dataPath column to existing projects...');

      // Add dataPath column
      db.exec('ALTER TABLE projects ADD COLUMN dataPath TEXT');

      // Update existing projects with dataPath
      const existingProjects = db.prepare('SELECT id, name FROM projects WHERE dataPath IS NULL').all() as any[];

      for (const project of existingProjects) {
        const timestamp = Date.now() + project.id; // Ensure uniqueness
        const sanitizedName = project.name.replace(/[^a-zA-Z0-9]/g, '_');
        const dataPath = path.join(process.cwd(), 'data', `project_${sanitizedName}_${timestamp}`);

        // Create directories
        const service = new SQLiteService();
        service.createProjectDirectories(dataPath);

        // Update database
        db.prepare('UPDATE projects SET dataPath = ? WHERE id = ?').run(dataPath, project.id);
        console.log(`✅ Migrated project "${project.name}" to ${dataPath}`);
      }
    }
  } catch (error) {
    console.error('[SQLite] Migration error:', error);
  }
};

migrateExistingProjects();

// --- Service Interface ---

export interface Project {
  id: number;
  name: string;
  path: string;
  dataPath: string;
  createdAt: string;
}

class SQLiteService {
  addProject(name: string, projectPath: string): Database.RunResult {
    // Create unique data directory for this project
    const timestamp = Date.now();
    const sanitizedName = name.replace(/[^a-zA-Z0-9]/g, '_');
    const dataPath = path.join(process.cwd(), 'data', `project_${sanitizedName}_${timestamp}`);

    // Create project directory structure
    this.createProjectDirectories(dataPath);

    const stmt = db.prepare('INSERT INTO projects (name, path, dataPath) VALUES (?, ?, ?)');
    return stmt.run(name, projectPath, dataPath);
  }

  createProjectDirectories(dataPath: string): void {
    // Create main project directory
    fs.mkdirSync(dataPath, { recursive: true });

    // Create subdirectories
    const subdirs = ['selected_files', 'chunks', 'vectors', 'metadata'];
    subdirs.forEach(subdir => {
      fs.mkdirSync(path.join(dataPath, subdir), { recursive: true });
    });

    console.log(`✅ Created project directories at: ${dataPath}`);
  }

  getProjectByName(name: string): Project | null {
    const stmt = db.prepare('SELECT * FROM projects WHERE name = ?');
    return (stmt.get(name) as Project) || null;
  }

  getProjectById(id: number): Project | null {
    const stmt = db.prepare('SELECT * FROM projects WHERE id = ?');
    return (stmt.get(id) as Project) || null;
  }

  getAllProjects(): Project[] {
    const stmt = db.prepare('SELECT * FROM projects ORDER BY name');
    return stmt.all() as Project[];
  }

  deleteProject(id: number): Database.RunResult {
    // Get project data before deletion
    const project = this.getProjectById(id);

    const stmt = db.prepare('DELETE FROM projects WHERE id = ?');
    const result = stmt.run(id);

    // Remove project directory if it exists
    if (project?.dataPath && fs.existsSync(project.dataPath)) {
      try {
        fs.rmSync(project.dataPath, { recursive: true, force: true });
        console.log(`✅ Removed project directory: ${project.dataPath}`);
      } catch (error) {
        console.error(`❌ Failed to remove project directory: ${project.dataPath}`, error);
      }
    }

    return result;
  }

  clearAllData(): void {
    try {
      // Очищаем все таблицы
      db.exec('DELETE FROM documents');
      db.exec('DELETE FROM projects');

      // Сбрасываем автоинкремент
      db.exec('DELETE FROM sqlite_sequence WHERE name IN ("projects", "documents")');

      console.log('[SQLite] All data cleared successfully');
    } catch (error) {
      console.error('[SQLite] Error clearing data:', error);
      throw error;
    }
  }
}

export const sqliteService = new SQLiteService();


---

// FILE: src\main\utils\environment.ts

export const isDev = process.env.NODE_ENV === 'development';
export const isProd = process.env.NODE_ENV === 'production';
export const isTest = process.env.NODE_ENV === 'test';

export const getAppDataPath = (): string => {
  const { app } = require('electron');
  return app.getPath('userData');
};

export const getResourcesPath = (): string => {
  const { app } = require('electron');
  return process.resourcesPath || app.getAppPath();
};

export const isMac = process.platform === 'darwin';
export const isWindows = process.platform === 'win32';
export const isLinux = process.platform === 'linux';


---

// FILE: src\main\utils\tokenizer.ts

/**
 * Utility for counting tokens in text
 * For MVP we use a simple estimation, can be replaced with Ollama API for accuracy
 */

// LangChain splitter removed - using simple estimation

export function countTokens(text: string): number {
  // Simple token estimation: ~4 characters per token
  return Math.ceil(text.length / 4);
}

export function estimateTokensFromLines(lines: number): number {
  // Rough estimation: average 15 tokens per line of code
  return lines * 15;
}

export function splitByTokenLimit(text: string, maxTokens: number, overlap: number = 0): string[] {
  const lines = text.split('\n');
  const chunks: string[] = [];
  let currentChunk: string[] = [];
  let currentTokens = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lineTokens = countTokens(line);
    
    // If adding this line would exceed the limit, save current chunk
    if (currentTokens + lineTokens > maxTokens && currentChunk.length > 0) {
      chunks.push(currentChunk.join('\n'));
      
      // Start new chunk with overlap
      if (overlap > 0 && currentChunk.length > overlap) {
        currentChunk = currentChunk.slice(-overlap);
        currentTokens = countTokens(currentChunk.join('\n'));
      } else {
        currentChunk = [];
        currentTokens = 0;
      }
    }
    
    currentChunk.push(line);
    currentTokens += lineTokens;
  }
  
  // Add the last chunk if it has content
  if (currentChunk.length > 0) {
    chunks.push(currentChunk.join('\n'));
  }
  
  return chunks;
}


---

// FILE: src\renderer\App.tsx

/**
 * @file src/renderer/App.tsx
 * @description Root React component for the application with tabbed project interface.
 */

import React, { useCallback, useEffect } from 'react';
import { Box, CssBaseline, ThemeProvider, createTheme, Typography, alpha, Button } from '@mui/material';
import { Add, FolderOpen } from '@mui/icons-material';

import { ProjectTabBar } from './components/ProjectTabBar';
import { Sidebar } from './components/Sidebar';
import { ProjectsPanel } from './components/ProjectsPanel';
import { FileExplorerPanel } from './components/ExplorerPanel/FileExplorerPanel.tsx';
import { DashboardPanel } from './components/DashboardPanel';
import { VectorizationPanel } from './components/VectorizationPanel/VectorizationPanel';
import { ChatPanel } from './components/ChatPanel';
import { SettingsPanel } from './components/SettingsPanel';
import { EmptyTabContent } from './components/EmptyTabContent';
import type { Project } from '../shared/ipc.d.ts';
import { useAppStore } from '../shared/store';

const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#9333ea',
      light: '#a855f7',
      dark: '#7c3aed',
    },
    secondary: {
      main: '#c084fc',
      light: '#d8b4fe',
      dark: '#a855f7',
    },
    background: {
      default: '#0f0f0f',
      paper: '#1a1a1a',
    },
    text: {
      primary: '#f1f5f9',
      secondary: '#94a3b8',
      disabled: '#64748b',
    },
    divider: '#2a2a2a',
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
      fontSize: '2.5rem',
    },
    h2: {
      fontWeight: 600,
      fontSize: '2rem',
    },
    h3: {
      fontWeight: 600,
      fontSize: '1.5rem',
    },
    h4: {
      fontWeight: 500,
      fontSize: '1.25rem',
    },
    body1: {
      fontSize: '0.875rem',
      lineHeight: 1.6,
    },
    body2: {
      fontSize: '0.75rem',
      lineHeight: 1.5,
    },
  },
  shape: {
    borderRadius: 12,
  },
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%)',
          fontFeatureSettings: '"cv02","cv03","cv04","cv11"',
        },
        '*::-webkit-scrollbar': {
          width: '8px',
          height: '8px',
        },
        '*::-webkit-scrollbar-track': {
          background: '#1a1a1a',
        },
        '*::-webkit-scrollbar-thumb': {
          background: '#9333ea',
          borderRadius: '4px',
          '&:hover': {
            background: '#a855f7',
          },
        },
      },
    },
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          borderRadius: 8,
          padding: '8px 16px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(147, 51, 234, 0.3)',
          },
        },
        contained: {
          background: 'linear-gradient(135deg, #9333ea 0%, #a855f7 100%)',
          '&:hover': {
            background: 'linear-gradient(135deg, #7c3aed 0%, #9333ea 100%)',
          },
        },
        outlined: {
          borderColor: '#9333ea',
          color: '#d8b4fe',
          '&:hover': {
            borderColor: '#a855f7',
            backgroundColor: alpha('#9333ea', 0.1),
          },
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backgroundColor: '#1a1a1a',
          border: '1px solid #2a2a2a',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        root: {
          minHeight: 48,
          backgroundColor: '#1a1a1a',
          borderBottom: '1px solid #2a2a2a',
        },
        indicator: {
          background: 'linear-gradient(90deg, #9333ea 0%, #a855f7 100%)',
          height: 3,
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 600,
          fontSize: '0.875rem',
          color: '#94a3b8',
          minHeight: 48,
          padding: '12px 16px',
          '&.Mui-selected': {
            color: '#d8b4fe',
          },
          '&:hover': {
            color: '#c084fc',
            backgroundColor: alpha('#9333ea', 0.1),
          },
        },
      },
    },
  },
});



// Define sidebar section types
type SidebarSection = 'files' | 'projects' | 'dashboard' | 'vectorization' | 'chat' | 'settings';

function App() {
  // Use Zustand store instead of local state
  const {
    tabs,
    activeTabId,
    activePanel,
    addTab,
    setActiveTab,
    removeTab,
    updateTab,
    setActivePanel
  } = useAppStore();

  // Get the currently active tab
  const activeTab = tabs.find(tab => tab.id === activeTabId) || null;

  // Handle file selection change
  const handleFileSelectionChange = useCallback((selection: Set<string>) => {
    if (activeTabId) {
      updateTab(activeTabId, { selectedFiles: selection });
    }
  }, [activeTabId, updateTab]);

  // Render the right panel based on selected sidebar section
  const renderRightPanel = () => {
    // Если активный таб не имеет проекта, показываем интерфейс выбора проекта
    if (activeTab && !activeTab.project) {
      return (
        <EmptyTabContent
          onProjectSelect={handleProjectSelectForActiveTab}
          openProjects={tabs.map(tab => tab.project ? {
            ...tab.project,
            id: Number(tab.project.id), // Convert back to number for IPC
            dataPath: tab.project.dataPath || '' // Ensure dataPath is string
          } : null)}
        />
      );
    }

    switch (activePanel) {
      case 'files':
        return activeTab?.project ? (
          <FileExplorerPanel
            project={activeTab.project}
            onSelectionChange={handleFileSelectionChange}
          />
        ) : !activeTab ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            textAlign: 'center',
            p: 4
          }}>
            <FolderOpen sx={{ fontSize: 64, mb: 2, color: '#374151' }} />
            <Typography variant="h6" sx={{ mb: 1, color: '#9ca3af' }}>
              Нет открытого проекта
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              Создайте новую вкладку для работы с проектом
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => createNewTab()}
              sx={{
                borderColor: '#9333ea',
                color: '#d8b4fe',
                '&:hover': {
                  borderColor: '#a855f7',
                  backgroundColor: alpha('#9333ea', 0.1)
                }
              }}
            >
              Новая вкладка
            </Button>
          </Box>
        ) : null;

      case 'projects':
        return (
          <ProjectsPanel
            onProjectSelect={handleProjectSelect}
            openProjects={tabs.map(tab => tab.project).filter(Boolean)}
          />
        );

      case 'dashboard':
        return activeTab?.project ? <DashboardPanel project={activeTab.project} /> : !activeTab ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            textAlign: 'center',
            p: 4
          }}>
            <Typography variant="h6" sx={{ mb: 1, color: '#9ca3af' }}>
              Нет открытого проекта
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              Создайте новую вкладку для работы с проектом
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => createNewTab()}
              sx={{
                borderColor: '#9333ea',
                color: '#d8b4fe',
                '&:hover': {
                  borderColor: '#a855f7',
                  backgroundColor: alpha('#9333ea', 0.1)
                }
              }}
            >
              Новая вкладка
            </Button>
          </Box>
        ) : null;

      case 'vectorization':
        return activeTab?.project ? (
          <VectorizationPanel
            project={{
              ...activeTab.project,
              id: Number(activeTab.project.id), // Convert string to number for IPC compatibility
              dataPath: activeTab.project.dataPath || ''
            }}
          />
        ) : !activeTab ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            textAlign: 'center',
            p: 4
          }}>
            <Typography variant="h6" sx={{ mb: 1, color: '#9ca3af' }}>
              Нет открытого проекта
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              Создайте новую вкладку для работы с проектом
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => createNewTab()}
              sx={{
                borderColor: '#9333ea',
                color: '#d8b4fe',
                '&:hover': {
                  borderColor: '#a855f7',
                  backgroundColor: alpha('#9333ea', 0.1)
                }
              }}
            >
              Новая вкладка
            </Button>
          </Box>
        ) : null;
      case 'chat':
        return activeTab?.project ? <ChatPanel project={activeTab.project} /> : !activeTab ? (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            textAlign: 'center',
            p: 4
          }}>
            <Typography variant="h6" sx={{ mb: 1, color: '#9ca3af' }}>
              Нет открытого проекта
            </Typography>
            <Typography variant="body2" sx={{ color: '#6b7280', mb: 3 }}>
              Создайте новую вкладку для работы с проектом
            </Typography>
            <Button
              variant="outlined"
              startIcon={<Add />}
              onClick={() => createNewTab()}
              sx={{
                borderColor: '#9333ea',
                color: '#d8b4fe',
                '&:hover': {
                  borderColor: '#a855f7',
                  backgroundColor: alpha('#9333ea', 0.1)
                }
              }}
            >
              Новая вкладка
            </Button>
          </Box>
        ) : null;
      case 'settings':
        return <SettingsPanel />;
      default:
        return (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#64748b',
            backgroundColor: '#0f0f0f',
            p: 3
          }}>
            <Typography variant="h6">
              Welcome to SmartRAG
            </Typography>
          </Box>
        );
    }
  };

  // Simple wrapper functions for store actions
  const switchToTab = useCallback((tabId: string) => {
    setActiveTab(tabId);
  }, [setActiveTab]);

  const createNewTab = useCallback((project?: Project | null) => {
    if (project && typeof project === 'object' && project.id) {
      const storeProject = {
        ...project,
        id: String(project.id)
      };
      addTab(storeProject);
    } else {
      // Create empty tab - we'll need to add this functionality to store
      console.log('Creating empty tab - functionality needs to be added to store');
    }
  }, [addTab]);

  const updateTabProject = useCallback((tabId: string, project: Project) => {
    const storeProject = {
      ...project,
      id: String(project.id)
    };
    updateTab(tabId, { project: storeProject });
  }, [updateTab]);

  const handleProjectSelectForActiveTab = useCallback((project: Project) => {
    // Convert IPC Project to Store Project (fix type mismatch)
    const storeProject = {
      ...project,
      id: String(project.id) // Convert number to string
    };

    if (activeTabId) {
      const activeTab = tabs.find(tab => tab.id === activeTabId);
      if (activeTab && !activeTab.project) {
        updateTab(activeTabId, { project: storeProject });
        return;
      }
    }

    addTab(storeProject);
  }, [activeTabId, tabs, updateTab, addTab]);

  const closeTab = useCallback((tabId: string) => {
    removeTab(tabId);
  }, [removeTab]);

  const handleFileSelection = useCallback((selection: Set<string>) => {
    if (activeTabId) {
      updateTab(activeTabId, { selectedFiles: selection });
    }
    console.log('Selected files:', Array.from(selection));
  }, [activeTabId, updateTab]);

  const handleProjectSelect = useCallback((project: Project) => {
    const storeProject = {
      ...project,
      id: String(project.id)
    };

    if (activeTabId) {
      updateTabProject(activeTabId, project);
    } else {
      createNewTab(project);
    }
  }, [activeTabId, updateTabProject, createNewTab]);



  // Store handles persistence automatically, no need for manual loading/saving

  // Local state for sidebar collapse (not persisted)
  const [sidebarCollapsed, setSidebarCollapsed] = React.useState(false);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+T: New tab
      if (event.ctrlKey && event.key === 't') {
        event.preventDefault();
        createNewTab();
      }

      // Ctrl+W: Close current tab
      if (event.ctrlKey && event.key === 'w' && activeTabId && tabs.length > 1) {
        event.preventDefault();
        closeTab(activeTabId);
      }

      // Ctrl+Tab: Switch to next tab
      if (event.ctrlKey && event.key === 'Tab') {
        event.preventDefault();
        const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
        const nextIndex = (currentIndex + 1) % tabs.length;
        if (tabs[nextIndex]) {
          switchToTab(tabs[nextIndex].id);
        }
      }

      // Ctrl+Shift+Tab: Switch to previous tab
      if (event.ctrlKey && event.shiftKey && event.key === 'Tab') {
        event.preventDefault();
        const currentIndex = tabs.findIndex(tab => tab.id === activeTabId);
        const prevIndex = currentIndex === 0 ? tabs.length - 1 : currentIndex - 1;
        if (tabs[prevIndex]) {
          switchToTab(tabs[prevIndex].id);
        }
      }

      // Ctrl+1-9: Switch to specific tab
      if (event.ctrlKey && event.key >= '1' && event.key <= '9') {
        event.preventDefault();
        const tabIndex = parseInt(event.key) - 1;
        if (tabs[tabIndex]) {
          switchToTab(tabs[tabIndex].id);
        }
      }

      // Ctrl+B: Toggle sidebar
      if (event.ctrlKey && event.key === 'b') {
        event.preventDefault();
        setSidebarCollapsed(!sidebarCollapsed);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [tabs, activeTabId, createNewTab, closeTab, switchToTab, sidebarCollapsed]);



  return (
    <ThemeProvider theme={darkTheme}>
      <CssBaseline />
      <Box sx={{ display: 'flex', height: '100vh' }}>
        {/* Left Sidebar */}
        <Sidebar
          sidebarSection={activePanel as SidebarSection}
          sidebarCollapsed={sidebarCollapsed}
          onSectionChange={(section) => setActivePanel(section)}
          onSidebarToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        />

        {/* Main Content Area */}
        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
          {/* Project Tab Bar - только если есть табы */}
          {tabs.length > 0 && (
            <ProjectTabBar
              tabs={tabs.map(tab => ({
                id: tab.id,
                project: tab.project,
                name: tab.project?.name || 'Untitled',
                isActive: tab.id === activeTabId,
                selectedFiles: tab.selectedFiles
              }))}
              activeTabId={activeTabId}
              onTabSwitch={switchToTab}
              onTabClose={closeTab}
              onNewTab={createNewTab}
              onProjectSelect={(tabId) => {
                console.log('Project select from tab:', tabId);
              }}
            />
          )}

          {/* Right Panel Content */}
          <Box sx={{ flex: 1, overflow: 'hidden' }}>
            {renderRightPanel()}
          </Box>
        </Box>


      </Box>
    </ThemeProvider>
  );
}

export default App;


---

// FILE: src\renderer\components\ChatPanel.tsx

/**
 * @file src/renderer/components/ChatPanel.tsx
 * @description Chat panel component for AI assistant interface
 */

import React from 'react';
import { Box, Typography } from '@mui/material';

interface ChatPanelProps {
  project?: any;
}

export const ChatPanel: React.FC<ChatPanelProps> = ({ project }) => {
  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>
      <Box sx={{
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center'
      }}>
        <Typography variant="body1" sx={{ color: '#64748b' }}>
          Chat interface will be implemented here
        </Typography>
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\components\DashboardPanel.tsx

/**
 * @file src/renderer/components/DashboardPanel.tsx
 * @description Dashboard panel component for indexing dashboard
 */

import React, { useState, useEffect, useRef } from 'react';
import { Box, Typography, LinearProgress, List, ListItem, ListItemText, Chip } from '@mui/material';
import type { IndexingUpdatePayload } from '../../shared/ipc.d';

interface LogEntry {
  id: number;
  timestamp: string;
  message: string;
  type: 'info' | 'error' | 'success';
}

interface DashboardPanelProps {
  project?: any;
}

export const DashboardPanel: React.FC<DashboardPanelProps> = ({ project }) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [currentProgress, setCurrentProgress] = useState<{ file: string; progress: number } | null>(null);
  const logContainerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const unsubscribe = window.electronAPI.onIndexingUpdate((payload: IndexingUpdatePayload) => {
      const timestamp = new Date().toLocaleTimeString();

      if (payload.status === 'progress') {
        setCurrentProgress({ file: payload.file, progress: payload.progress || 0 });
      } else {
        setCurrentProgress(null);

        const logEntry: LogEntry = {
          id: Date.now(),
          timestamp,
          message: payload.status === 'error'
            ? `Error indexing ${payload.file}: ${payload.error}`
            : `Successfully indexed ${payload.file}`,
          type: payload.status === 'error' ? 'error' : 'success'
        };

        setLogs(prev => [logEntry, ...prev].slice(0, 100)); // Keep last 100 logs
      }
    });

    return unsubscribe;
  }, []);

  // Auto-scroll to top when new logs are added
  useEffect(() => {
    if (logContainerRef.current) {
      logContainerRef.current.scrollTop = 0;
    }
  }, [logs]);

  return (
    <Box sx={{
      p: 3,
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      gap: 3,
      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%)',
    }}>

      {/* Current Progress */}
      {currentProgress && (
        <Box sx={{
          p: 3,
          backgroundColor: 'rgba(147, 51, 234, 0.1)',
          borderRadius: 2,
          border: '1px solid rgba(147, 51, 234, 0.2)'
        }}>
          <Typography variant="h6" sx={{ color: '#d8b4fe', mb: 2 }}>
            Currently Indexing
          </Typography>
          <Typography variant="body2" sx={{ color: '#94a3b8', mb: 2 }}>
            {currentProgress.file}
          </Typography>
          <LinearProgress
            variant="determinate"
            value={currentProgress.progress}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: 'rgba(147, 51, 234, 0.2)',
              '& .MuiLinearProgress-bar': {
                backgroundColor: '#9333ea',
                borderRadius: 4,
              }
            }}
          />
          <Typography variant="caption" sx={{ color: '#94a3b8', mt: 1, display: 'block' }}>
            {Math.round(currentProgress.progress)}% complete
          </Typography>
        </Box>
      )}

      {/* Activity Log */}
      <Box sx={{
        flex: 1,
        backgroundColor: 'rgba(15, 15, 15, 0.8)',
        borderRadius: 2,
        border: '1px solid rgba(147, 51, 234, 0.1)',
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column'
      }}>
        <Typography variant="h6" sx={{
          color: '#d8b4fe',
          p: 2,
          borderBottom: '1px solid rgba(147, 51, 234, 0.1)',
          backgroundColor: 'rgba(147, 51, 234, 0.05)'
        }}>
          Activity Log
        </Typography>

        <Box
          ref={logContainerRef}
          sx={{
            flex: 1,
            overflow: 'auto',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: 'rgba(147, 51, 234, 0.1)',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: 'rgba(147, 51, 234, 0.3)',
              borderRadius: '4px',
            },
          }}
        >
          {logs.length === 0 ? (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              color: '#64748b'
            }}>
              <Typography>No indexing activity yet. Start indexing a project to see logs here.</Typography>
            </Box>
          ) : (
            <List sx={{ p: 0 }}>
              {logs.map((log) => (
                <ListItem
                  key={log.id}
                  sx={{
                    borderBottom: '1px solid rgba(147, 51, 234, 0.05)',
                    '&:hover': {
                      backgroundColor: 'rgba(147, 51, 234, 0.05)'
                    }
                  }}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={log.type.toUpperCase()}
                          size="small"
                          sx={{
                            backgroundColor: log.type === 'error' ? 'rgba(239, 68, 68, 0.2)' : 'rgba(34, 197, 94, 0.2)',
                            color: log.type === 'error' ? '#ef4444' : '#22c55e',
                            fontWeight: 600,
                            fontSize: '0.7rem'
                          }}
                        />
                        <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                          {log.message}
                        </Typography>
                      </Box>
                    }
                    secondary={
                      <Typography variant="caption" sx={{ color: '#64748b' }}>
                        {log.timestamp}
                      </Typography>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}
        </Box>
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\components\EmptyTabContent.tsx

/**
 * @file src/renderer/components/EmptyTabContent.tsx
 * @description Content displayed in empty tabs (tabs without projects)
 */

import React from 'react';
import { Box, Typography } from '@mui/material';
import { ProjectsPanel } from './ProjectsPanel';
import type { Project } from '../../shared/ipc.d.ts';

interface EmptyTabContentProps {
  onProjectSelect: (project: Project) => void;
  openProjects: (Project | null)[];
}

export const EmptyTabContent: React.FC<EmptyTabContentProps> = ({
  onProjectSelect,
  openProjects
}) => {
  const handleProjectSelect = (project: Project) => {
    console.log('EmptyTabContent.handleProjectSelect called with:', project);
    onProjectSelect(project);
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#0f0f0f'
    }}>
      {/* Header */}
      <Box sx={{ 
        p: 3, 
        borderBottom: '1px solid #2a2a2a',
        backgroundColor: '#1a1a1a'
      }}>
        <Typography 
          variant="h5" 
          sx={{ 
            color: '#ffffff', 
            fontWeight: 600,
            mb: 1
          }}
        >
          Выберите проект
        </Typography>
        <Typography 
          variant="body2" 
          sx={{ 
            color: '#6b7280'
          }}
        >
          Выберите существующий проект или создайте новый для этой вкладки
        </Typography>
      </Box>

      {/* Projects Panel */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        <ProjectsPanel
          onProjectSelect={handleProjectSelect}
          openProjects={openProjects.filter(Boolean) as Project[]}
          disableAutoSelection={true}
        />
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\components\ExplorerPanel\ChunkingPanel.tsx

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Button, CircularProgress, Collapse, Chip, LinearProgress } from '@mui/material';
import type { Project } from '../../../shared/ipc';

interface ChunkingPanelProps {
  project: Project | null;
  selectedFileCount: number;
  selectedFilePaths: string[];
  onIndexingStateChange?: (isIndexing: boolean) => void;
}

import { formatDistanceToNow, format } from 'date-fns';
import { useDebouncedCallback } from 'use-debounce';
interface ChunkingResults {
  isChunked: boolean;
  showStats: boolean;
  results: {
    totalFiles: number;
    totalChunks: number;
    totalTokens: number;
    indexingTime: number;
    files: Array<{
      file: string;
      chunks: number;
      tokens: number;
    }>;
  } | null;
}

interface IndexingProgress {
  isIndexing: boolean;
  currentFile: string;
  progress: number;
  totalFiles: number;
  completedFiles: number;
  startTime?: number;
  elapsedTime?: number;
  estimatedRemainingTime?: number;
}

export const ChunkingPanel: React.FC<ChunkingPanelProps> = ({
  project,
  selectedFileCount,
  selectedFilePaths,
  onIndexingStateChange
}) => {
  const [indexingProgress, setIndexingProgress] = useState<IndexingProgress>({
    isIndexing: false,
    currentFile: '',
    progress: 0,
    totalFiles: 0,
    completedFiles: 0
  });

  const [chunkingResults, setChunkingResults] = useState<ChunkingResults>({
    isChunked: false,
    showStats: false,
    results: null
  });

  // Функции для сохранения/загрузки состояния чанкинга
  const saveChunkingState = useCallback((projectId: number, state: ChunkingResults) => {
    const key = `chunking_state_${projectId}`;
    localStorage.setItem(key, JSON.stringify(state));
  }, []);

  const loadChunkingState = useCallback((projectId: number): ChunkingResults | null => {
    const key = `chunking_state_${projectId}`;
    const saved = localStorage.getItem(key);
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (error) {
        console.error('Error loading chunking state:', error);
      }
    }
    return null;
  }, []);

  // Load chunking state when project changes
  useEffect(() => {
    if (project?.id) {
      const savedChunkingState = loadChunkingState(project.id);
      if (savedChunkingState && savedChunkingState.results) {
        setChunkingResults(savedChunkingState);
      } else {
        setChunkingResults({
          isChunked: false,
          showStats: false,
          results: null
        });
      }
    }
  }, [project?.id, loadChunkingState]);

  // Notify parent about indexing state changes
  useEffect(() => {
    onIndexingStateChange?.(indexingProgress.isIndexing);
  }, [indexingProgress.isIndexing, onIndexingStateChange]);

  // Listen for LangChain indexing updates
  useEffect(() => {
    if (!window.electronAPI.onLangChainIndexingUpdate) {
      console.warn('[ChunkingPanel] onLangChainIndexingUpdate not available');
      return;
    }

    const unsubscribe = window.electronAPI.onLangChainIndexingUpdate((payload: any) => {
      try {
        console.log('[ChunkingPanel] LangChain indexing update:', payload);

      if (payload.status === 'batch_progress') {
        const { batchProgress } = payload;
        setIndexingProgress(prev => ({
          ...prev,
          isIndexing: true,
          currentFile: batchProgress?.currentFile || '',
          progress: payload.progress || 0,
          completedFiles: batchProgress?.completed || 0,
          totalFiles: batchProgress?.total || prev.totalFiles,
          startTime: batchProgress?.startTime,
          elapsedTime: batchProgress?.elapsedTime,
          estimatedRemainingTime: batchProgress?.estimatedRemainingTime,
        }));
      } else if (payload.status === 'batch_completed') {
        setIndexingProgress(prev => ({
          ...prev,
          isIndexing: false,
          progress: 100,
          currentFile: 'Completed!',
        }));

        const newResults: ChunkingResults = {
          isChunked: true,
          showStats: true,
          results: payload.result,
        };
        setChunkingResults(newResults);
        if (project?.id) {
          saveChunkingState(project.id, newResults);
        }
      } else if (payload.status === 'cancelled') {
        console.log('[ChunkingPanel] Indexing was cancelled');
        setIndexingProgress(prev => ({
          ...prev,
          isIndexing: false,
          progress: 0,
          currentFile: 'Cancelled by user',
        }));
      } else if (payload.status === 'error') {
        setIndexingProgress(prev => ({
          ...prev,
          isIndexing: false,
          currentFile: `Error: ${payload.error}`,
        }));
      }
      } catch (error) {
        console.error('[ChunkingPanel] Error handling indexing update:', error);
      }
    });

    return unsubscribe;
  }, [project?.id, saveChunkingState]);

  const handleIndexSelected = useCallback(async () => {
    if (!project || selectedFileCount === 0) return;

    try {
      setIndexingProgress({
        isIndexing: true,
        currentFile: 'Preparing to index...',
        progress: 0,
        totalFiles: selectedFileCount,
        completedFiles: 0
      });

      // The IPC call will now trigger the background process
      await window.electronAPI.indexSelectedFiles({
        filePaths: selectedFilePaths,
        projectId: project.id,
      });

      // No longer setting isIndexing to false here.
      // This will be handled by the 'batch_completed' or 'error' event listener.

    } catch (error) {
      console.error('[ChunkingPanel] Error starting indexing:', error);
      setIndexingProgress({
        isIndexing: false,
        currentFile: `Error: ${(error as Error).message}`,
        progress: 0,
        totalFiles: selectedFileCount,
        completedFiles: 0,
      });
      alert('Error starting indexing: ' + (error as Error).message);
    }
  }, [project, selectedFileCount, selectedFilePaths]);

  const handleCancelIndexing = useCallback(async () => {
    if (!project) return;
    console.log('[ChunkingPanel] Cancelling indexing...');
    try {
      await window.electronAPI.cancelIndexing();
    } catch (error) {
      console.error('[ChunkingPanel] Error cancelling indexing:', error);
    }
  }, [project]);

  const clearChunkingState = useCallback(async () => {
    if (!project?.id) return;

    try {
      // Clear chunks files from disk
      const result = await window.electronAPI.clearProjectChunks(project.id);
      if (!result.success) {
        console.error('[ChunkingPanel] Error clearing chunks:', result.message);
        alert('Error clearing chunks: ' + result.message);
        return;
      }

      // Clear UI state
      const clearedState: ChunkingResults = { isChunked: false, showStats: false, results: null };
      setChunkingResults(clearedState);
      localStorage.removeItem(`chunking_state_${project.id}`);

      console.log('[ChunkingPanel] Chunks cleared successfully');
    } catch (error) {
      console.error('[ChunkingPanel] Error clearing chunks:', error);
      alert('Error clearing chunks: ' + (error as Error).message);
    }
  }, [project?.id]);

  if (selectedFileCount === 0) {
    return null;
  }

  return (
    <Box>
      {/* Compact control buttons in one row */}
      <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
        <Button
          variant="contained"
          onClick={handleIndexSelected}
          disabled={selectedFileCount === 0 || indexingProgress.isIndexing}
          sx={{
            flex: 1,
            backgroundColor: chunkingResults.isChunked ? '#059669' : '#9333ea',
            borderRadius: '6px',
            textTransform: 'none',
            fontWeight: 500,
            fontSize: '0.75rem',
            py: 0.75,
            '&:hover': {
              backgroundColor: chunkingResults.isChunked ? '#047857' : '#7e22ce'
            },
            '&.Mui-disabled': {
              backgroundColor: 'rgba(147, 51, 234, 0.2)',
              color: 'rgba(216, 180, 254, 0.5)'
            }
          }}
        >
          {indexingProgress.isIndexing ? (
            <>
              <CircularProgress size={12} sx={{ mr: 0.5, color: 'white' }} />
              Indexing...
            </>
          ) : chunkingResults.isChunked ? (
            `Re-index ${selectedFileCount}`
          ) : (
            `Index ${selectedFileCount}`
          )}
        </Button>

        {indexingProgress.isIndexing && (
          <Button
            variant="outlined"
            onClick={handleCancelIndexing}
            sx={{
              color: '#f43f5e',
              borderColor: 'rgba(244, 63, 94, 0.3)',
              borderRadius: '6px',
              textTransform: 'none',
              fontWeight: 500,
              fontSize: '0.75rem',
              py: 0.75,
              '&:hover': {
                backgroundColor: 'rgba(244, 63, 94, 0.1)',
                borderColor: 'rgba(244, 63, 94, 0.5)',
              },
            }}
          >
            Cancel
          </Button>
        )}

        {chunkingResults.isChunked && !indexingProgress.isIndexing && (
          <>
            <Button
              size="small"
              variant="outlined"
              onClick={() => setChunkingResults(prev => ({ ...prev, showStats: !prev.showStats }))}
              sx={{
                color: '#059669',
                borderColor: 'rgba(5, 150, 105, 0.3)',
                backgroundColor: 'rgba(5, 150, 105, 0.05)',
                borderRadius: '6px',
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '0.75rem',
                py: 0.5,
                px: 1.5,
                '&:hover': {
                  backgroundColor: 'rgba(5, 150, 105, 0.1)',
                  borderColor: 'rgba(5, 150, 105, 0.5)',
                  color: '#10b981'
                }
              }}
            >
              {chunkingResults.showStats ? 'Hide' : 'Show'} Stats
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={clearChunkingState}
              sx={{
                color: '#dc2626',
                borderColor: 'rgba(220, 38, 38, 0.3)',
                backgroundColor: 'rgba(220, 38, 38, 0.05)',
                borderRadius: '6px',
                textTransform: 'none',
                fontWeight: 500,
                fontSize: '0.75rem',
                py: 0.5,
                px: 1.5,
                '&:hover': {
                  backgroundColor: 'rgba(220, 38, 38, 0.1)',
                  borderColor: 'rgba(220, 38, 38, 0.5)',
                  color: '#ef4444'
                }
              }}
            >
              Clear
            </Button>
          </>
        )}
      </Box>

      {/* Indexing Progress */}
      {indexingProgress.isIndexing && (
        <Box sx={{ mt: 2, p: 2, backgroundColor: 'rgba(147, 51, 234, 0.1)', borderRadius: 1, border: '1px solid rgba(147, 51, 234, 0.2)' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" sx={{ color: '#d8b4fe', fontWeight: 500 }}>
              {indexingProgress.progress < 100 ? 'Indexing...' : 'Completing...'}
            </Typography>
            <Typography variant="caption" sx={{ color: '#9ca3af' }}>
              {indexingProgress.completedFiles} / {indexingProgress.totalFiles} files
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={indexingProgress.progress}
            sx={{
              height: 6,
              borderRadius: 3,
              backgroundColor: 'rgba(147, 51, 234, 0.2)',
              '& .MuiLinearProgress-bar': {
                background: 'linear-gradient(90deg, #9333ea, #a855f7)',
                borderRadius: 3,
              }
            }}
          />
          <Typography variant="caption" sx={{ color: '#9ca3af', mt: 1, display: 'block', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {indexingProgress.currentFile}
          </Typography>
          {indexingProgress.startTime && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
              <Typography variant="caption" sx={{ color: '#9ca3af' }}>
                Started: {format(new Date(indexingProgress.startTime), 'HH:mm:ss')}
              </Typography>
              <Typography variant="caption" sx={{ color: '#9ca3af' }}>
                Elapsed: {formatDistanceToNow(new Date(Date.now() - (indexingProgress.elapsedTime || 0)), { includeSeconds: true, addSuffix: false })}
              </Typography>
              <Typography variant="caption" sx={{ color: '#9ca3af' }}>
                ETA: {formatDistanceToNow(new Date(Date.now() + (indexingProgress.estimatedRemainingTime || 0)), { includeSeconds: true, addSuffix: true })}
              </Typography>
            </Box>
          )}
        </Box>
      )}

      {/* Chunking Stats */}
      {chunkingResults.isChunked && (
        <Box sx={{ mt: 1 }}>
          <Collapse in={chunkingResults.showStats}>
            <Box sx={{
              p: 1.5,
              backgroundColor: 'rgba(5, 150, 105, 0.05)',
              borderRadius: 1,
              border: '1px solid rgba(5, 150, 105, 0.2)'
            }}>
              {(() => {
                try {
                  const stats = chunkingResults.results;
                  if (!stats) return null;

                  const { totalFiles, totalChunks, indexingTime } = stats;
                  const totalTokens = stats.totalTokens || 0;
                  const avgTokensPerChunk = totalChunks > 0 ? Math.round(totalTokens / totalChunks) : 0;

                  return (
                    <Box>
                      <Typography variant="body2" sx={{ color: '#10b981', mb: 1.5, fontWeight: 600 }}>
                        Indexing Complete
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        <Chip label={`Time: ${(indexingTime / 1000).toFixed(2)}s`} size="small" variant="outlined" sx={{ borderColor: 'rgba(5, 150, 105, 0.3)', color: '#34d399' }} />
                        <Chip label={`${totalFiles} files`} size="small" variant="outlined" sx={{ borderColor: 'rgba(59, 130, 246, 0.3)', color: '#60a5fa' }} />
                        <Chip label={`${totalChunks} chunks`} size="small" variant="outlined" sx={{ borderColor: 'rgba(59, 130, 246, 0.3)', color: '#60a5fa' }} />
                        <Chip label={`${totalTokens.toLocaleString()} tokens`} size="small" variant="outlined" sx={{ borderColor: 'rgba(234, 179, 8, 0.3)', color: '#facc15' }} />
                        <Chip label={`~${avgTokensPerChunk} avg`} size="small" variant="outlined" sx={{ borderColor: 'rgba(107, 114, 128, 0.3)', color: '#9ca3af' }} />
                      </Box>
                    </Box>
                  );
                } catch (error) {
                  console.error('Error rendering chunking statistics:', error);
                  return (
                    <Typography variant="caption" color="error">
                      Error loading statistics
                    </Typography>
                  );
                }
              })()}
            </Box>
          </Collapse>
        </Box>
      )}
    </Box>
  );
};


---

// FILE: src\renderer\components\ExplorerPanel\FileExplorerPanel.tsx

/**
 * @file src/renderer/components/FileExplorerPanel.tsx
 * @description File explorer panel component for browsing project files
 */

import React, { useState } from 'react';
import { Box, Typography } from '@mui/material';
import { FileTree } from './FileTree';
import { ChunkingPanel } from './ChunkingPanel';
import { useAppStore } from '../../../shared/store';

interface FileExplorerPanelProps {
  project: any | null;
  onSelectionChange: (selection: Set<string>) => void;
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B (0 chars)';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  const sizeStr = parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  return `${sizeStr} (${bytes.toLocaleString()} chars)`;
};

export const FileExplorerPanel: React.FC<FileExplorerPanelProps> = ({
  project,
  onSelectionChange
}) => {
  // Use store instead of local state
  const {
    selectedFilePaths,
    totalSelectedSize,
    totalSelectedTokens
  } = useAppStore();

  const [selectedFileCount, setSelectedFileCount] = useState(0);
  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>

      
      {project ? (
        <Box sx={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
          minHeight: 0 // Важно для правильного flex-shrink
        }}>
          <Typography
            variant="body2"
            sx={{
              color: '#94a3b8',
              mb: 2,
              fontFamily: 'monospace',
              backgroundColor: 'rgba(147, 51, 234, 0.1)',
              p: 1,
              borderRadius: 1,
              flexShrink: 0 // Не сжимать этот элемент
            }}
          >
            {project.path}
          </Typography>
          <Box sx={{
            flex: 1,
            overflow: 'hidden',
            minHeight: 0 // Важно для правильного flex-shrink
          }}>
            <FileTree
              project={project}
              onSelectionChange={onSelectionChange}
              onSelectedFilesChange={(filePaths) => {
                setSelectedFileCount(filePaths.length);
              }}
            />
          </Box>

          {/* Chunking Panel */}
          {selectedFileCount > 0 && (
            <Box sx={{ mt: 2, flexShrink: 0 }}>
              <ChunkingPanel
                project={project}
                selectedFileCount={selectedFileCount}
                selectedFilePaths={selectedFilePaths}
              />
            </Box>
          )}
        </Box>
      ) : (
        <Box sx={{ 
          flex: 1, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          textAlign: 'center'
        }}>
          <Typography variant="body1" sx={{ color: '#64748b' }}>
            No project selected. Please select a project from the Projects panel.
          </Typography>
        </Box>
      )}
    </Box>
  );
};


---

// FILE: src\renderer\components\ExplorerPanel\FileTree.tsx

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Tree } from 'antd';
import type { TreeProps, DataNode as AntdDataNode } from 'antd/es/tree';
import { Box, Typography, Button, CircularProgress } from '@mui/material';
import { GlobalStyles } from '@mui/system';
import type { Project, FileTreeNode } from '../../../shared/ipc';
import { useAppStore } from '../../../shared/store';

import { FilterSettingsDialog } from './FilterSettingsDialog';
import { formatFileSize, formatCharCount, formatTokenCount, formatFileCount, estimateTokenCount } from '../../../shared/utils/formatters';
import { shouldIgnoreFile } from '../../../shared/utils/fileFilters';
import { useGitignore } from '../../../shared/hooks/useGitignore';
import { useDebouncedCallback } from 'use-debounce';

interface FileTreeProps {
  project: Project | null;
  onSelectionChange: (selected: Set<string>) => void;
  onSizeChange?: (totalSize: number) => void;
  onSelectedFilesChange?: (filePaths: string[]) => void;
}

// Extend Antd's DataNode to include our custom properties for type safety
interface CustomDataNode extends AntdDataNode {
  size?: number;
  tokenCount?: number;
  isLeaf?: boolean;
}





const formatNodeInfo = (bytes: number, tokens?: number): string => {
  if (bytes === 0) return '0 B • 0 chars • 0 tokens';
  const size = formatFileSize(bytes);
  const chars = formatCharCount(bytes);
  const tokenStr = formatTokenCount(tokens || 0);
  return `${size} • ${chars} • ${tokenStr}`;
};

const getTokenCountColor = (tokens?: number): string => {
  const tokenCount = tokens || 0;
  if (tokenCount > 50000) {
    return '#ff4444'; // Red for >50K tokens
  } else if (tokenCount > 25000) {
    return '#ff8800'; // Orange for >25K tokens
  }
  return 'text.secondary'; // Default color
};

const ICONS = {
  folder: (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 00-1.883 2.542l.857 6a2.25 2.25 0 002.227 1.932H19.05a2.25 2.25 0 002.227-1.932l.857-6a2.25 2.25 0 00-1.883-2.542m-16.5 0V6.226c0-1.232.996-2.226 2.226-2.226h4.126c.51 0 1.009.21 1.355.587l.274.274a.688.688 0 01.487.205h4.127c1.23 0 2.226.994 2.226 2.226v3.55M16.5 12.75a.75.75 0 00-1.5 0v2.25a.75.75 0 001.5 0v-2.25z" />
    </svg>
  ),
  folderOpen: (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 00-1.883 2.542l.857 6a2.25 2.25 0 002.227 1.932H19.05a2.25 2.25 0 002.227-1.932l.857-6a2.25 2.25 0 00-1.883-2.542m-16.5 0V6.226c0-1.232.996-2.226 2.226-2.226h4.126c.51 0 1.009.21 1.355.587l.274.274a.688.688 0 01.487.205h4.127c1.23 0 2.226.994 2.226 2.226v3.55" />
    </svg>
  ),
  file: (
    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={1.5}>
      <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
    </svg>
  ),
  chevronRight: <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={3}><path strokeLinecap="round" strokeLinejoin="round" d="M9 5l7 7-7 7" /></svg>,
  chevronDown: <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={3}><path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" /></svg>,
};

// Convert FileTreeNode to antd Tree format
const convertToAntdFormat = (nodes: FileTreeNode[]): CustomDataNode[] => {
  // Sort: folders first, then files
  const sorted = [...nodes].sort((a, b) => {
    const aIsDir = a.isDirectory || (a.children && a.children.length > 0);
    const bIsDir = b.isDirectory || (b.children && b.children.length > 0);

    if (aIsDir && !bIsDir) return -1;
    if (!aIsDir && bIsDir) return 1;
    return a.name.localeCompare(b.name);
  });

  return sorted.map(node => {
    const isDirectory = node.isDirectory || (node.children && node.children.length > 0);
    return {
      key: node.id,
      title: node.name,
      children: node.children ? convertToAntdFormat(node.children) : undefined,
      isLeaf: !isDirectory,
      size: node.size,
      tokenCount: node.tokenCount,
    };
  });
};

export const FileTree = ({ project, onSelectionChange, onSizeChange, onSelectedFilesChange }: FileTreeProps) => {
  // Zustand store
  const {
    setSelectedFiles,
    setSelectedFilePaths,
    setTotalSelectedSize,
    autoSelectFilters
  } = useAppStore();

  // Gitignore hook
  const { gitignorePatterns } = useGitignore(project?.path);


  const [loading, setLoading] = useState(false);
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([]);
  const [treeData, setTreeData] = useState<CustomDataNode[]>([]);
  const [flatNodes, setFlatNodes] = useState<Map<React.Key, CustomDataNode>>(new Map());
  const [selectionLoaded, setSelectionLoaded] = useState(false);
  const [filterDialogOpen, setFilterDialogOpen] = useState(false);



  // Функции для сохранения/загрузки выбора файлов
  const saveSelection = useCallback((projectId: number, selection: React.Key[]) => {
    const key = `fileTree_selection_${projectId}`;
    localStorage.setItem(key, JSON.stringify(selection));
  }, []);

  const loadSelection = useCallback((projectId: number): React.Key[] => {
    const key = `fileTree_selection_${projectId}`;
    const saved = localStorage.getItem(key);
    if (saved) {
      try {
        return JSON.parse(saved);
      } catch (error) {
        console.error('Error loading saved selection:', error);
      }
    }
    return [];
  }, []);



  const selectedFileCount = useMemo(() => {
    return checkedKeys.filter(key => flatNodes.get(key)?.isLeaf).length;
  }, [checkedKeys, flatNodes]);

  const currentSelectedFilePaths = useMemo(() => {
    return checkedKeys.filter(key => flatNodes.get(key)?.isLeaf).map(key => String(key));
  }, [checkedKeys, flatNodes]);

  const { totalSelectedSize, totalSelectedTokens } = useMemo(() => {
    const leafNodes = checkedKeys.map(key => flatNodes.get(key)).filter((node): node is CustomDataNode => !!(node && node.isLeaf));
    const size = leafNodes.reduce((sum, node) => sum + (node.size || 0), 0);
    const tokens = leafNodes.reduce((sum, node) => sum + (node.tokenCount || (node.size ? estimateTokenCount(node.size) : 0)), 0);
    return { totalSelectedSize: size, totalSelectedTokens: tokens };
  }, [checkedKeys, flatNodes]);

  // Notify parent about selected files changes
  useEffect(() => {
    onSelectedFilesChange?.(currentSelectedFilePaths);
  }, [currentSelectedFilePaths, onSelectedFilesChange]);

  const folderSizeStats = useMemo(() => {
    const stats = new Map<React.Key, { totalSize: number; totalTokens: number }>();

    const traverse = (node: CustomDataNode): { totalSize: number; totalTokens: number } => {
      if (stats.has(node.key)) {
        return stats.get(node.key)!;
      }

      if (node.isLeaf) {
        return { totalSize: node.size || 0, totalTokens: node.tokenCount || 0 };
      }

      let childrenTotalSize = 0;
      let childrenTotalTokens = 0;
      if (node.children) {
        for (const child of node.children) {
          const childStats = traverse(child as CustomDataNode);
          childrenTotalSize += childStats.totalSize;
          childrenTotalTokens += childStats.totalTokens;
        }
      }

      const result = { totalSize: childrenTotalSize, totalTokens: childrenTotalTokens };
      stats.set(node.key, result);
      return result;
    };

    treeData.forEach(node => traverse(node));
    return stats;
  }, [treeData]);



  // Load the full file tree
  useEffect(() => {
    if (!project?.path) {
      return;
    }

    const loadFiles = async () => {
      setLoading(true);
      try {
        const files = await window.electronAPI.readDirectory(project.path);
        if (files) {
          const antdData = convertToAntdFormat(files);
          const flatNodeMap = new Map<React.Key, CustomDataNode>();
          
          const traverse = (nodes: CustomDataNode[]) => {
            nodes.forEach(node => {
              flatNodeMap.set(node.key, node);
              if (node.children) traverse(node.children);
            });
          };
          traverse(antdData);
          setFlatNodes(flatNodeMap);
          setTreeData(antdData);

          // Загружаем сохраненный выбор для этого проекта
          const savedSelection = loadSelection(project.id);
          if (savedSelection.length > 0) {
            setCheckedKeys(savedSelection);
            setSelectionLoaded(true);
          } else {
            setSelectionLoaded(false);
          }
        }
      } catch (error) {
        console.error('Failed to load files:', error);
      } finally {
        setLoading(false);
      }
    };

    loadFiles();
  }, [project?.path, project?.id, loadSelection]); // Re-run when project path or id changes

  // Auto-select after data is loaded (только если не загружен сохраненный выбор)
  useEffect(() => {
    if (treeData.length > 0 && !selectionLoaded) {
      handleAutoSelect();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [treeData, selectionLoaded]); // Run only when treeData is populated and no saved selection

  // Обновляем родительский компонент при загрузке сохраненного выбора
  useEffect(() => {
    if (selectionLoaded && checkedKeys.length > 0) {
      // Вызываем onSelectionChange напрямую для сохраненного выбора
      const leafNodes = checkedKeys.map(key => flatNodes.get(key)).filter((node): node is CustomDataNode => !!(node && node.isLeaf));
      const leafKeys = new Set(leafNodes.map(node => node.key as string));
      onSelectionChange(leafKeys);

      const totalSize = leafNodes.reduce((sum, node) => sum + (node.size || 0), 0);
      setTotalSelectedSize(totalSize);
      if (onSizeChange) {
        onSizeChange(totalSize);
      }
    }
  }, [selectionLoaded, checkedKeys, flatNodes, onSelectionChange, onSizeChange]);

  const updateSelection = useCallback((keys: React.Key[]) => {
    setCheckedKeys(keys);

    const leafNodes = keys.map(key => flatNodes.get(key)).filter((node): node is CustomDataNode => !!(node && node.isLeaf));
    const leafKeys = new Set(leafNodes.map(node => node.key as string));
    const leafPaths = leafNodes.map(node => node.key as string);

    // Update store
    setSelectedFiles(leafKeys);
    setSelectedFilePaths(leafPaths);

    // Update parent components
    onSelectionChange(leafKeys);

    const totalSize = leafNodes.reduce((sum, node) => sum + (node.size || 0), 0);
    setTotalSelectedSize(totalSize);
    if (onSizeChange) {
      onSizeChange(totalSize);
    }
  }, [flatNodes, onSelectionChange, onSizeChange, setSelectedFiles, setSelectedFilePaths, setTotalSelectedSize]);

  const debouncedOnSelectionChange = useDebouncedCallback(
    (keys: React.Key[]) => {
      updateSelection(keys);
    },
    200
  );

  const handleCheck: TreeProps['onCheck'] = (keys) => {
    const keyArray = keys as React.Key[];
    setCheckedKeys(keyArray);
    debouncedOnSelectionChange(keyArray);

    // Сохраняем выбор для текущего проекта
    if (project) {
      saveSelection(project.id, keyArray);
    }
  };

  const titleRender = (node: CustomDataNode) => {
    const isFolder = !node.isLeaf;
    let sizeInBytes = 0;
    let tokenCount = 0;

    if (isFolder) {
      const stats = folderSizeStats.get(node.key);
      if (stats) {
        sizeInBytes = stats.totalSize;
        tokenCount = stats.totalTokens;
      }
    } else {
      sizeInBytes = node.size || 0;
      tokenCount = node.tokenCount || 0;
    }

    const finalTokenCount = tokenCount > 0 ? tokenCount : estimateTokenCount(sizeInBytes);
    const counterText = formatNodeInfo(sizeInBytes, finalTokenCount);
    const tokenColor = getTokenCountColor(finalTokenCount);

    return (
      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', overflow: 'hidden' }}>
        <Box component="span" sx={{ display: 'inline-flex', alignItems: 'center', mr: 1 }}>
          {isFolder ? ICONS.folder : ICONS.file}
        </Box>
        <Typography
          component="span"
          sx={{ flexGrow: 1, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
        >
          {typeof node.title === 'function' ? node.title(node) : node.title}
        </Typography>
        {counterText && (
          <Typography
            variant="caption"
            component="span"
            sx={{
              color: tokenColor,
              ml: 2,
              pr: 1,
              whiteSpace: 'nowrap',
              fontWeight: tokenColor !== 'text.secondary' ? 'bold' : 'normal'
            }}
          >
            {counterText}
          </Typography>
        )}
      </Box>
    );
  };


  // Control buttons
  const handleAutoSelect = useCallback(() => {
    if (flatNodes.size === 0) return;
    const autoSelectIds: string[] = [];
    const collectAutoSelectLeaves = (nodes: CustomDataNode[], currentPath: string = '') => {
      nodes.forEach(node => {
        if (!node) return;

        const nodePath = currentPath ? `${currentPath}/${node.title}` : node.title as string;
        const isLeaf = node.isLeaf || false;

        if (shouldIgnoreFile(node.title as string, isLeaf, nodePath, gitignorePatterns, autoSelectFilters)) {
          return;
        }

        if (isLeaf) {
          autoSelectIds.push(node.key as string);
        }

        if (node.children) {
          collectAutoSelectLeaves(node.children as CustomDataNode[], nodePath);
        }
      });
    };
    collectAutoSelectLeaves(treeData);
    updateSelection(autoSelectIds);
  }, [treeData, flatNodes.size, updateSelection, gitignorePatterns, autoSelectFilters]);

  const handleSelectAll = useCallback(() => {
    const allLeafKeys = Array.from(flatNodes.values()).filter(n => n.isLeaf).map(n => n.key);
    updateSelection(allLeafKeys);
  }, [flatNodes, updateSelection]);

  const handleUnselectAll = useCallback(() => {
    updateSelection([]);

    // Также очищаем сохраненный выбор
    if (project) {
      saveSelection(project.id, []);
    }
  }, [updateSelection, project, saveSelection]);





  if (loading) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%', color: 'text.secondary' }}>
        <CircularProgress color="inherit" size={24} sx={{ mr: 2 }} />
        <Typography>Loading file tree...</Typography>
      </Box>
    );
  }

  if (!project) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%', color: 'text.secondary' }}>
        <Typography>No project selected</Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', minHeight: 0 }}>
      {/* Inject styles to override antd's default theme */}
      <GlobalStyles styles={{
        '.ant-tree': {
          background: 'transparent',
          color: '#d1d5db', // text-gray-300
          fontSize: '0.875rem',
        },
        '.ant-tree .ant-tree-node-content-wrapper': {
          '&:hover': { backgroundColor: 'rgba(147, 51, 234, 0.1) !important' },
        },
        '.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected': {
          backgroundColor: 'rgba(147, 51, 234, 0.2) !important',
        },
        '.ant-tree-checkbox-checked .ant-tree-checkbox-inner': {
          backgroundColor: '#9333ea',
          borderColor: '#9333ea',
        },
        '.ant-tree-checkbox-indeterminate .ant-tree-checkbox-inner::after': {
          backgroundColor: '#9333ea',
        },
      }} />

      {/* Control buttons and counters */}
      <Box sx={{ px: 1, py: 0.5, borderBottom: '1px solid', borderColor: 'rgba(147, 51, 234, 0.1)', flexShrink: 0 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 1 }}>
          {/* Selection buttons */}
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            <Box sx={{ display: 'flex', alignItems: 'stretch' }}>
              <Button
                size="small"
                variant="outlined"
                onClick={handleAutoSelect}
                sx={{
                  minWidth: 'auto',
                  px: 1.5,
                  py: 0.5,
                  fontSize: '0.75rem',
                  color: '#a855f7',
                  borderColor: 'rgba(168, 85, 247, 0.3)',
                  backgroundColor: 'rgba(168, 85, 247, 0.05)',
                  borderRadius: '6px 0 0 6px',
                  textTransform: 'none',
                  fontWeight: 500,
                  borderRight: 'none',
                  '&:hover': {
                    backgroundColor: 'rgba(168, 85, 247, 0.1)',
                    borderColor: 'rgba(168, 85, 247, 0.5)',
                    color: '#c084fc'
                  }
                }}
              >
                Auto Select
              </Button>
              <Button
                size="small"
                variant="outlined"
                onClick={() => setFilterDialogOpen(true)}
                sx={{
                  minWidth: 'auto',
                  px: 0.5,
                  py: 0.5,
                  fontSize: '0.75rem',
                  color: '#a855f7',
                  borderColor: 'rgba(168, 85, 247, 0.3)',
                  backgroundColor: 'rgba(168, 85, 247, 0.05)',
                  borderRadius: '0 6px 6px 0',
                  textTransform: 'none',
                  fontWeight: 500,
                  '&:hover': {
                    backgroundColor: 'rgba(168, 85, 247, 0.1)',
                    borderColor: 'rgba(168, 85, 247, 0.5)',
                    color: '#c084fc'
                  }
                }}
                title="Filter Settings"
              >
                ⚙️
              </Button>
            </Box>
            <Button
              size="small"
              variant="outlined"
              onClick={handleSelectAll}
              sx={{
                minWidth: 'auto',
                px: 1.5,
                py: 0.5,
                fontSize: '0.75rem',
                color: '#10b981',
                borderColor: 'rgba(16, 185, 129, 0.3)',
                backgroundColor: 'rgba(16, 185, 129, 0.05)',
                borderRadius: '6px',
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': {
                  backgroundColor: 'rgba(16, 185, 129, 0.1)',
                  borderColor: 'rgba(16, 185, 129, 0.5)',
                  color: '#34d399'
                }
              }}
            >
              Select All
            </Button>
            <Button
              size="small"
              variant="outlined"
              onClick={handleUnselectAll}
              sx={{
                minWidth: 'auto',
                px: 1.5,
                py: 0.5,
                fontSize: '0.75rem',
                color: '#f59e0b',
                borderColor: 'rgba(245, 158, 11, 0.3)',
                backgroundColor: 'rgba(245, 158, 11, 0.05)',
                borderRadius: '6px',
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': {
                  backgroundColor: 'rgba(245, 158, 11, 0.1)',
                  borderColor: 'rgba(245, 158, 11, 0.5)',
                  color: '#fbbf24'
                }
              }}
            >
              Clear All
            </Button>
          </Box>

          {/* Counters */}
          {selectedFileCount > 0 && (
            <Box sx={{
              display: 'flex',
              gap: 1.5,
              alignItems: 'center',
              p: 1,
              backgroundColor: 'rgba(59, 130, 246, 0.08)',
              borderRadius: 1,
              border: '1px solid rgba(59, 130, 246, 0.2)'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Typography variant="caption" sx={{ color: '#3b82f6', fontWeight: 600 }}>
                  {formatFileCount(selectedFileCount)}
                </Typography>
                <Typography variant="caption" sx={{ color: '#64748b' }}>
                  files
                </Typography>
              </Box>
              <Typography variant="caption" sx={{ color: '#64748b' }}>•</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Typography variant="caption" sx={{ color: '#10b981', fontWeight: 600 }}>
                  {formatFileSize(totalSelectedSize)}
                </Typography>
                <Typography variant="caption" sx={{ color: '#64748b' }}>
                  size
                </Typography>
              </Box>
              <Typography variant="caption" sx={{ color: '#64748b' }}>•</Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Typography variant="caption" sx={{ color: '#f59e0b', fontWeight: 600 }}>
                  {formatTokenCount(totalSelectedTokens)}
                </Typography>
                <Typography variant="caption" sx={{ color: '#64748b' }}>
                  tokens
                </Typography>
              </Box>
            </Box>
          )}
        </Box>
      </Box>

      {/* File tree */}
      <Box sx={{ flex: 1, overflow: 'auto', minHeight: 0,
        '&::-webkit-scrollbar': { width: '8px' },
        '&::-webkit-scrollbar-track': { backgroundColor: 'rgba(147, 51, 234, 0.1)' },
        '&::-webkit-scrollbar-thumb': { backgroundColor: 'rgba(147, 51, 234, 0.3)', borderRadius: '4px' },
      }}>
        {treeData.length > 0 && (
          <Tree
            checkable
            blockNode
            treeData={treeData}
            onCheck={handleCheck}
            checkedKeys={checkedKeys}
            titleRender={titleRender}
            switcherIcon={(props) => props.isLeaf ? null : (props.expanded ? ICONS.chevronDown : ICONS.chevronRight)}
          />
        )}
      </Box>

      {/* Filter Settings Dialog */}
      <FilterSettingsDialog
        open={filterDialogOpen}
        onClose={() => setFilterDialogOpen(false)}
      />

    </Box>
  );
};


---

// FILE: src\renderer\components\ExplorerPanel\FilterSettingsDialog.tsx

/**
 * @file src/renderer/components/ExplorerPanel/FilterSettingsDialog.tsx
 * @description Compact dialog for configuring auto-select filters
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Chip,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';
import {
  RestoreOutlined,
  TuneOutlined
} from '@mui/icons-material';
import { useAppStore } from '../../../shared/store';
import type { AutoSelectFilters } from '../../../shared/types/filters.types';
import { DEFAULT_AUTO_SELECT_FILTERS, FILTER_PRESETS } from '../../../shared/types/filters.types';

interface FilterSettingsDialogProps {
  open: boolean;
  onClose: () => void;
}

export const FilterSettingsDialog: React.FC<FilterSettingsDialogProps> = ({ open, onClose }) => {
  const { autoSelectFilters, setAutoSelectFilters } = useAppStore();
  const [localFilters, setLocalFilters] = useState<AutoSelectFilters>(autoSelectFilters);

  // Sync with store when dialog opens
  useEffect(() => {
    if (open) {
      setLocalFilters(autoSelectFilters);
    }
  }, [open, autoSelectFilters]);

  const handleSave = () => {
    setAutoSelectFilters(localFilters);
    onClose();
  };

  const handleReset = () => {
    setLocalFilters(DEFAULT_AUTO_SELECT_FILTERS);
  };

  const handlePresetApply = (presetId: string) => {
    const preset = FILTER_PRESETS.find(p => p.id === presetId);
    if (preset) {
      setLocalFilters(preset.filters);
    }
  };

  const updateFilter = (path: string, value: boolean) => {
    setLocalFilters(prev => {
      const keys = path.split('.');
      const newFilters = { ...prev };
      let current: any = newFilters;

      for (let i = 0; i < keys.length - 1; i++) {
        if (!current[keys[i]]) {
          current[keys[i]] = {};
        } else {
          current[keys[i]] = { ...current[keys[i]] };
        }
        current = current[keys[i]];
      }

      current[keys[keys.length - 1]] = value;
      return newFilters;
    });
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          backgroundColor: '#1a1a1a',
          border: '1px solid rgba(168, 85, 247, 0.3)',
          borderRadius: 2
        }
      }}
    >
      <DialogTitle sx={{
        color: '#a855f7',
        display: 'flex',
        alignItems: 'center',
        gap: 1,
        borderBottom: '1px solid rgba(168, 85, 247, 0.2)',
        pb: 1,
        fontSize: '1.1rem'
      }}>
        <TuneOutlined />
        Filter Settings
        <Tooltip title="Reset to defaults">
          <IconButton
            onClick={handleReset}
            sx={{ ml: 'auto', color: '#64748b' }}
          >
            <RestoreOutlined />
          </IconButton>
        </Tooltip>
      </DialogTitle>

      <DialogContent sx={{ p: 2 }}>
        {/* Quick Presets */}
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" sx={{ color: '#d8b4fe', mb: 1 }}>
            Quick Presets
          </Typography>
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {FILTER_PRESETS.map(preset => (
              <Chip
                key={preset.id}
                label={preset.name}
                onClick={() => handlePresetApply(preset.id)}
                size="small"
                sx={{
                  backgroundColor: 'rgba(168, 85, 247, 0.1)',
                  color: '#c084fc',
                  border: '1px solid rgba(168, 85, 247, 0.3)',
                  '&:hover': {
                    backgroundColor: 'rgba(168, 85, 247, 0.2)'
                  }
                }}
              />
            ))}
          </Box>
        </Box>

        <Divider sx={{ borderColor: 'rgba(168, 85, 247, 0.2)', mb: 2 }} />

        {/* File Types */}
        <Typography variant="subtitle2" sx={{ color: '#d8b4fe', mb: 1 }}>
          File Types
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1, mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.images.enabled}
                onChange={(e) => updateFilter('fileTypes.images.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Images <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    ({localFilters.fileTypes.images.extensions.join(', ')})
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.vectors.enabled}
                onChange={(e) => updateFilter('fileTypes.vectors.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  SVG <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    ({localFilters.fileTypes.vectors.extensions.join(', ')})
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.logs.enabled}
                onChange={(e) => updateFilter('fileTypes.logs.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Logs <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    ({localFilters.fileTypes.logs.extensions.join(', ')})
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.lockFiles.enabled}
                onChange={(e) => updateFilter('fileTypes.lockFiles.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Lock Files <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (package-lock.json, yarn.lock, etc.)
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.binary.enabled}
                onChange={(e) => updateFilter('fileTypes.binary.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Binary <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    ({localFilters.fileTypes.binary.extensions.join(', ')})
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.fileTypes.icons.enabled}
                onChange={(e) => updateFilter('fileTypes.icons.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Icons <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (**/icons/**, favicon.*, etc.)
                  </Typography>
                </Typography>
              </Box>
            }
          />
        </Box>

        <Divider sx={{ borderColor: 'rgba(168, 85, 247, 0.2)', mb: 2 }} />

        {/* Directories */}
        <Typography variant="subtitle2" sx={{ color: '#d8b4fe', mb: 1 }}>
          Directories
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1, mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.directories.nodeModules.enabled}
                onChange={(e) => updateFilter('directories.nodeModules.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  node_modules <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (node_modules/)
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.directories.buildOutputs.enabled}
                onChange={(e) => updateFilter('directories.buildOutputs.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Build Outputs <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (dist/, build/, out/)
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.directories.versionControl.enabled}
                onChange={(e) => updateFilter('directories.versionControl.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Version Control <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (.git/, .svn/, .hg/)
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.directories.ideFiles.enabled}
                onChange={(e) => updateFilter('directories.ideFiles.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  IDE Files <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (.vscode/, .idea/, __pycache__/)
                  </Typography>
                </Typography>
              </Box>
            }
          />
        </Box>

        <Divider sx={{ borderColor: 'rgba(168, 85, 247, 0.2)', mb: 2 }} />

        {/* Other Settings */}
        <Typography variant="subtitle2" sx={{ color: '#d8b4fe', mb: 1 }}>
          Other Settings
        </Typography>
        <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 1, mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.hiddenFiles.enabled}
                onChange={(e) => updateFilter('hiddenFiles.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Hidden Files <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (.*files)
                  </Typography>
                </Typography>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={localFilters.gitignore?.enabled ?? false}
                onChange={(e) => updateFilter('gitignore.enabled', e.target.checked)}
                size="small"
                sx={{ '& .MuiSwitch-thumb': { backgroundColor: '#a855f7' } }}
              />
            }
            label={
              <Box>
                <Typography variant="body2" sx={{ color: '#e2e8f0' }}>
                  Use .gitignore <Typography component="span" sx={{ color: '#64748b', fontSize: '0.75rem' }}>
                    (auto-load patterns)
                  </Typography>
                </Typography>
              </Box>
            }
          />
        </Box>

        <Typography variant="caption" sx={{ color: '#64748b', display: 'block', textAlign: 'center' }}>
          Filters only affect Auto Select. Manual selection always available.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ p: 2, borderTop: '1px solid rgba(168, 85, 247, 0.2)' }}>
        <Button 
          onClick={onClose}
          sx={{ color: '#64748b' }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleSave}
          variant="contained"
          sx={{
            backgroundColor: '#a855f7',
            '&:hover': { backgroundColor: '#9333ea' }
          }}
        >
          Save Settings
        </Button>
      </DialogActions>
    </Dialog>
  );
};


---

// FILE: src\renderer\components\ExplorerPanel\ProjectExplorer.tsx

/**
 * @file src/renderer/components/ProjectExplorer.tsx
 * @description Ultra-optimized project manager with Tailwind v4, GPU acceleration, and smooth animations
 */

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import type { Project } from '../../../shared/ipc';
import '../../styles/globals.css';

// Optimized SVG icons for maximum performance
const ICONS = {
  add: `<svg class="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path></svg>`,
  delete: `<svg class="size-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg>`,
  folder: `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path d="M2 6a2 2 0 012-2h5l2 2h5a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"></path></svg>`,
  folderOpen: `<svg class="size-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M2 6a2 2 0 012-2h4l2 2h4a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" clip-rule="evenodd"></path></svg>`,
  storage: `<svg class="size-12" fill="currentColor" viewBox="0 0 20 20"><path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2zM3 16a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z"></path></svg>`,
} as const;

interface ProjectExplorerProps {
  onProjectSelect: (project: Project | null) => void;
  openProjects?: Project[];
  disableAutoSelection?: boolean;
}

export const ProjectExplorer = ({ onProjectSelect, openProjects = [], disableAutoSelection = false }: ProjectExplorerProps) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [hoveredProjectId, setHoveredProjectId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [hasManualSelection, setHasManualSelection] = useState(false);

  // Optimized project fetching with error handling
  const fetchProjects = useCallback(async () => {
    try {
      setIsLoading(true);
      const projs = await window.electronAPI.getAllProjects();
      setProjects(projs);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Separate effect for initial project selection
  useEffect(() => {
    fetchProjects();
  }, [fetchProjects]);

  // Handle initial project selection separately
  useEffect(() => {
    console.log('Project selection effect triggered:', {
      projectsLength: projects.length,
      selectedProjectId,
      hasManualSelection,
      projects: projects.map(p => ({ id: p.id, name: p.name }))
    });

    if (projects.length > 0 && selectedProjectId === null && !hasManualSelection && !disableAutoSelection) {
      console.log('Auto-selecting first project');
      const firstProject = projects[0];
      setSelectedProjectId(firstProject.id);
      onProjectSelect(firstProject);
    } else if (projects.length === 0 && selectedProjectId !== null) {
      console.log('Clearing selection - no projects');
      setSelectedProjectId(null);
      onProjectSelect(null);
      setHasManualSelection(false);
    }
  }, [projects, selectedProjectId, onProjectSelect, hasManualSelection]);

  // Optimized event handlers with loading states
  const handleAddProject = useCallback(async (event?: React.MouseEvent) => {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    try {
      setIsLoading(true);
      console.log('Opening directory dialog...');
      const directoryPath = await window.electronAPI.openDirectoryDialog();
      console.log('Directory selected:', directoryPath);

      if (directoryPath) {
        console.log('Adding project...');
        const newProject = await window.electronAPI.addProject(directoryPath);
        console.log('Project added:', newProject);
        await fetchProjects();

        // Автоматически выбираем новый добавленный проект
        if (newProject) {
          setSelectedProjectId(newProject.id);
          setHasManualSelection(true);
          onProjectSelect(newProject);
        }
      } else {
        console.log('No directory selected');
      }
    } catch (error) {
      console.error('Failed to add project:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchProjects, onProjectSelect]);

  const handleDeleteProject = useCallback(async (projectId: number, event: React.MouseEvent) => {
    event.stopPropagation();
    try {
      setIsLoading(true);
      await window.electronAPI.deleteProject(projectId);
      await fetchProjects();
    } catch (error) {
      console.error('Failed to delete project:', error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchProjects]);

  const handleSelectProject = useCallback((project: Project) => {
    console.log('handleSelectProject called with:', project);
    setSelectedProjectId(project.id);
    setHasManualSelection(true);
    onProjectSelect(project);
  }, [onProjectSelect]);

  // Memoized project list for better performance
  const projectList = useMemo(() => projects, [projects]);

  return (
    <div className="p-4 h-full flex flex-col gpu-layer">
      {/* Add button */}
      <div className="flex justify-end items-center mb-6 pb-3 border-b border-primary-500/20">
        <button
          onClick={handleAddProject}
          disabled={isLoading}
          className="
            group relative flex items-center gap-2 px-4 py-2
            bg-gradient-to-r from-primary-600 to-primary-500
            hover:from-primary-700 hover:to-primary-600
            disabled:from-gray-600 disabled:to-gray-500
            text-white text-sm font-medium rounded-lg
            transition-all duration-200 ease-out
            shadow-lg shadow-primary-500/25 hover:shadow-primary-500/40
            transform hover:scale-105 active:scale-95
            focus-ring gpu-layer
          "
          title="Add new project"
        >
          <div
            className={`transition-transform duration-200 ${isLoading ? 'animate-spin' : 'group-hover:scale-110'}`}
            dangerouslySetInnerHTML={{ __html: ICONS.add }}
          />
          <span>Add</span>

          {/* Subtle glow effect */}
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary-600 to-primary-500 opacity-0 group-hover:opacity-20 transition-opacity duration-200 blur-sm -z-10" />
        </button>
      </div>

      {/* Project list with optimized scrolling */}
      <div className="flex-1 overflow-y-auto scrollbar-ultra-smooth">
        {projectList.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-full text-center py-8 animate-fade-in gpu-layer">
            <div
              className="mb-4 text-gray-600 opacity-60"
              dangerouslySetInnerHTML={{ __html: ICONS.storage }}
            />
            <h3 className="text-base font-medium text-gray-400 mb-1">
              No projects yet
            </h3>
            <p className="text-sm text-gray-500">
              Click "Add" to get started
            </p>
          </div>
        ) : (
          <div className="space-y-2">
            {projectList.map((project, index) => (
              <div
                key={project.id}
                className="
                  group relative animate-slide-in gpu-layer
                  transition-all duration-200 ease-out
                  hover:transform hover:scale-[1.02] hover:-translate-y-0.5
                "
                style={{ animationDelay: `${index * 50}ms` }}
                onMouseEnter={() => setHoveredProjectId(project.id)}
                onMouseLeave={() => setHoveredProjectId(null)}
              >
                <button
                  onClick={() => handleSelectProject(project)}
                  className={`
                    w-full text-left p-4 rounded-xl border transition-all duration-200 ease-out
                    backdrop-blur-sm focus-ring gpu-layer relative overflow-hidden
                    ${selectedProjectId === project.id
                      ? 'bg-gradient-to-br from-primary-500/20 to-primary-600/10 border-primary-500/40 shadow-lg shadow-primary-500/20'
                      : 'bg-gray-800/40 border-gray-700/30 hover:bg-gray-800/60 hover:border-gray-600/50'
                    }
                  `}
                >
                  <div className="flex items-center gap-3 relative z-10">
                    {/* Project Icon */}
                    <div
                      className={`
                        transition-all duration-200 ease-out
                        ${selectedProjectId === project.id ? 'text-primary-300 scale-110' : 'text-gray-400'}
                      `}
                      dangerouslySetInnerHTML={{
                        __html: selectedProjectId === project.id ? ICONS.folderOpen : ICONS.folder
                      }}
                    />

                    {/* Project Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className={`
                        text-sm font-medium mb-1 transition-colors duration-200
                        ${selectedProjectId === project.id ? 'text-primary-200' : 'text-gray-200'}
                      `}>
                        {project.name}
                      </h3>

                      <p className="text-xs text-gray-500 font-mono truncate mb-2">
                        {project.path}
                      </p>

                      <div className="flex items-center gap-2">
                        {(() => {
                          const isOpen = openProjects.some(openProject => openProject?.path === project.path);
                          return (
                            <span className={`
                              inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium
                              transition-colors duration-200
                              ${isOpen
                                ? 'bg-green-500/20 text-green-300 border border-green-500/30'
                                : selectedProjectId === project.id
                                  ? 'bg-primary-500/20 text-primary-300 border border-primary-500/30'
                                  : 'bg-gray-700/50 text-gray-400 border border-gray-600/30'
                              }
                            `}>
                              {isOpen ? 'Open' : 'Ready'}
                            </span>
                          );
                        })()}
                      </div>
                    </div>
                  </div>

                  {/* Subtle background glow for selected project */}
                  {selectedProjectId === project.id && (
                    <div className="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-primary-600/5 rounded-xl opacity-50" />
                  )}
                </button>
                {/* Delete button with smooth animations - исправлен размер */}
                <button
                  onClick={(e) => handleDeleteProject(project.id, e)}
                  disabled={isLoading}
                  className={`
                    absolute top-2 right-2 p-1.5 rounded-md
                    transition-all duration-200 ease-out z-20
                    hover:bg-red-500/20 hover:scale-110 active:scale-95
                    focus-ring disabled:opacity-50 disabled:cursor-not-allowed
                    ${hoveredProjectId === project.id
                      ? 'opacity-100 text-red-400'
                      : 'opacity-0 text-transparent pointer-events-none'
                    }
                  `}
                  title="Delete project"
                >
                  <div dangerouslySetInnerHTML={{ __html: ICONS.delete }} />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};


---

// FILE: src\renderer\components\ProjectTabBar.tsx

/**
 * @file src/renderer/components/ProjectTabBar.tsx
 * @description Tab bar component for managing multiple project tabs
 */

import React, { useState, useRef, useEffect } from 'react';
import { 
  Box, 
  Tab, 
  Tabs, 
  IconButton, 
  Tooltip, 
  Typography,
  Menu,
  MenuItem,
  alpha
} from '@mui/material';
import { 
  Add as AddIcon, 
  Close as CloseIcon, 
  MoreHoriz as MoreIcon,
  FolderOpen as FolderIcon
} from '@mui/icons-material';

interface ProjectTab {
  id: string;
  project: any | null;
  name: string;
  isActive: boolean;
  selectedFiles: Set<string>;
}

interface ProjectTabBarProps {
  tabs: ProjectTab[];
  activeTabId: string | null;
  onTabSwitch: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onNewTab: () => void;
  onProjectSelect: (tabId: string) => void;
}

export const ProjectTabBar: React.FC<ProjectTabBarProps> = ({
  tabs,
  activeTabId,
  onTabSwitch,
  onTabClose,
  onNewTab,
  onProjectSelect
}) => {
  const [overflowMenuAnchor, setOverflowMenuAnchor] = useState<null | HTMLElement>(null);
  const [draggedTab, setDraggedTab] = useState<string | null>(null);
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const [showOverflow, setShowOverflow] = useState(false);

  // Check if tabs overflow the container
  useEffect(() => {
    const checkOverflow = () => {
      if (tabsContainerRef.current) {
        const container = tabsContainerRef.current;
        const hasOverflow = container.scrollWidth > container.clientWidth;
        setShowOverflow(prev => {
          // Only update if the value actually changed to prevent infinite re-renders
          if (prev !== hasOverflow) {
            return hasOverflow;
          }
          return prev;
        });
      }
    };

    // Use a timeout to ensure DOM is ready
    const timeoutId = setTimeout(checkOverflow, 0);

    // Use ResizeObserver for better performance if available
    let resizeObserver: ResizeObserver | null = null;
    if (tabsContainerRef.current && window.ResizeObserver) {
      resizeObserver = new ResizeObserver(checkOverflow);
      resizeObserver.observe(tabsContainerRef.current);
    } else {
      // Fallback to window resize event
      window.addEventListener('resize', checkOverflow);
    }

    return () => {
      clearTimeout(timeoutId);
      if (resizeObserver) {
        resizeObserver.disconnect();
      } else {
        window.removeEventListener('resize', checkOverflow);
      }
    };
  }, [tabs.length]); // Only depend on tabs.length instead of the entire tabs array

  const handleTabClose = (e: React.MouseEvent, tabId: string) => {
    e.stopPropagation();
    onTabClose(tabId);
  };

  const handleTabClick = (tabId: string) => {
    onTabSwitch(tabId);
  };

  const handleOverflowMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setOverflowMenuAnchor(event.currentTarget);
  };

  const handleOverflowMenuClose = () => {
    setOverflowMenuAnchor(null);
  };

  const truncateTabName = (name: string | undefined, maxLength: number = 20) => {
    if (!name) return 'Untitled';
    return name.length > maxLength ? `${name.substring(0, maxLength)}...` : name;
  };

  return (
    <Box
      role="tablist"
      aria-label="Project tabs"
      sx={{
        display: 'flex',
        alignItems: 'center',
        backgroundColor: '#1a1a1a',
        borderBottom: '1px solid rgba(147, 51, 234, 0.1)',
        height: 50,
        minHeight: 50,
        maxHeight: 50,
        overflow: 'hidden'
      }}
    >
      {/* Tabs Container */}
      <Box
        ref={tabsContainerRef}
        sx={{
          flex: 1,
          display: 'flex',
          overflowX: 'auto',
          overflowY: 'hidden',
          scrollbarWidth: 'none',
          '&::-webkit-scrollbar': { display: 'none' }
        }}
      >
        {tabs.map((tab, index) => (
          <Box
            key={tab.id}
            onClick={() => handleTabClick(tab.id)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleTabClick(tab.id);
              }
            }}
            tabIndex={0}
            role="tab"
            aria-selected={tab.id === activeTabId}
            aria-label={`Project tab: ${tab.name}${tab.project ? ` (${tab.project.path})` : ''}`}
            sx={{
              display: 'flex',
              alignItems: 'center',
              minWidth: 120,
              maxWidth: 200,
              height: 50,
              px: 2,
              cursor: 'pointer',
              backgroundColor: tab.id === activeTabId
                ? alpha('#9333ea', 0.2)
                : 'transparent',
              borderRight: '1px solid rgba(147, 51, 234, 0.1)',
              borderTop: tab.id === activeTabId
                ? '2px solid #9333ea'
                : '2px solid transparent',
              transition: 'all 0.2s ease',
              '&:hover': {
                backgroundColor: alpha('#9333ea', 0.1),
              },
              '&:focus': {
                outline: '2px solid #9333ea',
                outlineOffset: '-2px',
              },
              position: 'relative'
            }}
          >
            {/* Tab Icon */}
            <FolderIcon 
              sx={{ 
                fontSize: 16, 
                mr: 1, 
                color: tab.project ? '#9333ea' : '#64748b',
                opacity: 0.8
              }} 
            />
            
            {/* Tab Name */}
            <Typography
              variant="body2"
              sx={{
                flex: 1,
                color: tab.id === activeTabId ? '#d8b4fe' : '#94a3b8',
                fontWeight: tab.id === activeTabId ? 600 : 400,
                fontSize: '0.875rem',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
            >
              {truncateTabName(tab.name)}
            </Typography>

            {/* Close Button */}
            {tabs.length > 1 && (
              <IconButton
                size="small"
                onClick={(e) => handleTabClose(e, tab.id)}
                aria-label={`Close tab: ${tab.name}`}
                sx={{
                  ml: 1,
                  p: 0.5,
                  color: '#64748b',
                  opacity: 0.6,
                  '&:hover': {
                    opacity: 1,
                    color: '#f87171',
                    backgroundColor: alpha('#f87171', 0.1)
                  },
                  '&:focus': {
                    outline: '2px solid #f87171',
                    outlineOffset: '2px',
                  }
                }}
              >
                <CloseIcon sx={{ fontSize: 14 }} />
              </IconButton>
            )}
          </Box>
        ))}
      </Box>

      {/* Overflow Menu Button */}
      {showOverflow && (
        <IconButton
          onClick={handleOverflowMenuOpen}
          sx={{
            color: '#94a3b8',
            '&:hover': { color: '#d8b4fe' }
          }}
        >
          <MoreIcon />
        </IconButton>
      )}

      {/* New Tab Button */}
      <Tooltip title="New Tab (Ctrl+T)">
        <IconButton
          onClick={onNewTab}
          sx={{
            mx: 1,
            color: '#94a3b8',
            '&:hover': {
              color: '#d8b4fe',
              backgroundColor: alpha('#9333ea', 0.1)
            }
          }}
        >
          <AddIcon />
        </IconButton>
      </Tooltip>

      {/* Overflow Menu */}
      <Menu
        anchorEl={overflowMenuAnchor}
        open={Boolean(overflowMenuAnchor)}
        onClose={handleOverflowMenuClose}
        PaperProps={{
          sx: {
            backgroundColor: '#1a1a1a',
            border: '1px solid rgba(147, 51, 234, 0.2)',
            '& .MuiMenuItem-root': {
              color: '#94a3b8',
              '&:hover': {
                backgroundColor: alpha('#9333ea', 0.1),
                color: '#d8b4fe'
              }
            }
          }
        }}
      >
        {tabs.map((tab) => (
          <MenuItem
            key={tab.id}
            onClick={() => {
              handleTabClick(tab.id);
              handleOverflowMenuClose();
            }}
            selected={tab.id === activeTabId}
          >
            <FolderIcon sx={{ fontSize: 16, mr: 1 }} />
            {tab.name}
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};


---

// FILE: src\renderer\components\ProjectsPanel.tsx

/**
 * @file src/renderer/components/ProjectsPanel.tsx
 * @description Projects panel component for managing and selecting projects
 */

import React from 'react';
import { Box, Typography } from '@mui/material';
import { ProjectExplorer } from './ExplorerPanel/ProjectExplorer';

interface ProjectsPanelProps {
  onProjectSelect: (project: any) => void;
  openProjects?: any[]; // Список уже открытых проектов
  disableAutoSelection?: boolean;
}

export const ProjectsPanel: React.FC<ProjectsPanelProps> = ({
  onProjectSelect,
  openProjects = [],
  disableAutoSelection = false
}) => {
  const handleProjectSelect = (project: any) => {
    console.log('ProjectsPanel.handleProjectSelect called with:', project);
    onProjectSelect(project);
  };

  return (
    <Box sx={{ 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>
      <Box sx={{ flex: 1, overflow: 'auto' }}>
        <ProjectExplorer
          onProjectSelect={handleProjectSelect}
          openProjects={openProjects}
          disableAutoSelection={disableAutoSelection}
        />
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\components\QueryInterface.tsx

/**
 * @file src/renderer/components/QueryInterface.tsx
 * @description React component for manually testing the MCP API.
 */

import React, { useState } from 'react';
import {
  Box,
  Button,
  CircularProgress,
  Paper,
  TextField,
  Typography,
} from '@mui/material';

export const QueryInterface = () => {
  const [query, setQuery] = useState('');
  const [response, setResponse] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleQuery = async () => {
    if (!query.trim()) return;
    setIsLoading(true);
    setResponse('');
    try {
      // This simulates a direct call to the local MCP API
      const res = await fetch('http://127.0.0.1:4888/mcp/v1/context', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ question: query }),
      });
      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }
      const data = await res.json();
      setResponse(JSON.stringify(data, null, 2));
    } catch (error: any) {
      setResponse(`Error: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        Manual API Test
      </Typography>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        <TextField
          label="Enter your query"
          variant="outlined"
          fullWidth
          multiline
          rows={3}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
        />
        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            variant="contained"
            onClick={handleQuery}
            disabled={isLoading}
            startIcon={isLoading ? <CircularProgress size={20} /> : null}
          >
            {isLoading ? 'Querying...' : 'Send Query'}
          </Button>
        </Box>
        {response && (
          <Paper
            variant="outlined"
            sx={{ p: 2, mt: 2, backgroundColor: 'grey.100', maxHeight: 300, overflowY: 'auto' }}
          >
            <Typography variant="subtitle2" gutterBottom>Response:</Typography>
            <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
              {response}
            </pre>
          </Paper>
        )}
      </Box>
    </Paper>
  );
};


---

// FILE: src\renderer\components\SettingsPanel.tsx

/**
 * @file src/renderer/components/SettingsPanel.tsx
 * @description Settings panel component for application configuration
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Card,
  CardContent
} from '@mui/material';
import { RestartAlt, Warning } from '@mui/icons-material';

export const SettingsPanel: React.FC = () => {
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [isResetting, setIsResetting] = useState(false);

  const handleFactoryReset = async () => {
    setIsResetting(true);

    try {
      // Очищаем localStorage
      localStorage.clear();
      console.log('Cleared localStorage');

      // Очищаем данные Electron через IPC
      if (window.electronAPI?.clearAppData) {
        await window.electronAPI.clearAppData();
        console.log('Cleared Electron app data');
      }

      // Показываем уведомление и перезагружаем приложение
      setTimeout(() => {
        window.location.reload();
      }, 1000);

    } catch (error) {
      console.error('Error during factory reset:', error);
    } finally {
      setIsResetting(false);
      setResetDialogOpen(false);
    }
  };

  return (
    <Box sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column',
      backgroundColor: '#0f0f0f',
      p: 3
    }}>
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', gap: 3 }}>
        {/* Раздел сброса данных */}
        <Card sx={{
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          border: '1px solid rgba(239, 68, 68, 0.3)',
          borderRadius: 2
        }}>
          <CardContent>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Warning sx={{ color: '#ef4444' }} />
              <Typography variant="h6" sx={{ color: '#ef4444', fontWeight: 600 }}>
                Danger Zone
              </Typography>
            </Box>

            <Typography variant="body2" sx={{ color: '#94a3b8', mb: 3 }}>
              Reset all application data to factory defaults. This will remove all projects,
              tabs, settings, and cached data. This action cannot be undone.
            </Typography>

            <Button
              variant="outlined"
              color="error"
              startIcon={<RestartAlt />}
              onClick={() => setResetDialogOpen(true)}
              sx={{
                borderColor: '#ef4444',
                color: '#ef4444',
                '&:hover': {
                  borderColor: '#dc2626',
                  backgroundColor: 'rgba(239, 68, 68, 0.1)'
                }
              }}
            >
              Factory Reset
            </Button>
          </CardContent>
        </Card>

        {/* Placeholder для других настроек */}
        <Card sx={{
          backgroundColor: 'rgba(147, 51, 234, 0.1)',
          border: '1px solid rgba(147, 51, 234, 0.3)',
          borderRadius: 2
        }}>
          <CardContent>
            <Typography variant="h6" sx={{ color: '#d8b4fe', fontWeight: 600, mb: 2 }}>
              General Settings
            </Typography>
            <Typography variant="body2" sx={{ color: '#64748b' }}>
              Additional application settings will be implemented here
            </Typography>
          </CardContent>
        </Card>
      </Box>

      {/* Диалог подтверждения сброса */}
      <Dialog
        open={resetDialogOpen}
        onClose={() => setResetDialogOpen(false)}
        sx={{
          '& .MuiDialog-paper': {
            backgroundColor: '#1a1a1a',
            border: '1px solid rgba(239, 68, 68, 0.3)'
          }
        }}
      >
        <DialogTitle sx={{ color: '#ef4444', display: 'flex', alignItems: 'center', gap: 1 }}>
          <Warning />
          Factory Reset Confirmation
        </DialogTitle>
        <DialogContent>
          <Alert severity="error" sx={{ mb: 2 }}>
            This action will permanently delete all application data!
          </Alert>
          <Typography sx={{ color: '#94a3b8' }}>
            Are you sure you want to reset SmartRAG to factory defaults? This will:
          </Typography>
          <Box component="ul" sx={{ color: '#94a3b8', mt: 1, pl: 2 }}>
            <li>Remove all project tabs and data</li>
            <li>Clear all cached files and indexes</li>
            <li>Reset all application settings</li>
            <li>Clear localStorage and Electron data</li>
          </Box>
          <Typography sx={{ color: '#ef4444', mt: 2, fontWeight: 600 }}>
            This action cannot be undone!
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setResetDialogOpen(false)}
            sx={{ color: '#94a3b8' }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleFactoryReset}
            color="error"
            variant="contained"
            disabled={isResetting}
            startIcon={<RestartAlt />}
          >
            {isResetting ? 'Resetting...' : 'Reset to Factory Defaults'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};


---

// FILE: src\renderer\components\Sidebar.tsx

/**
 * @file src/renderer/components/Sidebar.tsx
 * @description Left sidebar navigation component with collapsible sections
 */

import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  IconButton,
  Tooltip,
  Typography,
  alpha
} from '@mui/material';
import {
  FolderOpen as FilesIcon,
  Folder as ProjectsIcon,
  Dashboard as DashboardIcon,
  Chat as ChatIcon,
  Settings as SettingsIcon,
  ChevronLeft as CollapseIcon,
  ChevronRight as ExpandIcon,
  ScatterPlot as VectorizationIcon
} from '@mui/icons-material';

type SidebarSection = 'files' | 'projects' | 'dashboard' | 'vectorization' | 'chat' | 'settings';

interface SidebarProps {
  sidebarSection: SidebarSection;
  sidebarCollapsed: boolean;
  onSectionChange: (section: SidebarSection) => void;
  onSidebarToggle: () => void;
}

const sidebarSections = [
  { id: 'projects' as SidebarSection, label: 'Projects', icon: ProjectsIcon },
  { id: 'files' as SidebarSection, label: 'File Explorer', icon: FilesIcon },
  { id: 'dashboard' as SidebarSection, label: 'Dashboard', icon: DashboardIcon },
  { id: 'vectorization' as SidebarSection, label: 'Vectorization', icon: VectorizationIcon },
  { id: 'chat' as SidebarSection, label: 'Chat', icon: ChatIcon },
  { id: 'settings' as SidebarSection, label: 'Settings', icon: SettingsIcon },
];

export const Sidebar: React.FC<SidebarProps> = ({
  sidebarSection,
  sidebarCollapsed,
  onSectionChange,
  onSidebarToggle
}) => {
  const handleSectionClick = (section: SidebarSection) => {
    onSectionChange(section);
  };



  const sidebarWidth = sidebarCollapsed ? 60 : 180;

  return (
    <Box
      role="navigation"
      aria-label="Main navigation sidebar"
      sx={{
        width: sidebarWidth,
        height: '100vh',
        backgroundColor: '#1a1a1a',
        borderRight: '1px solid rgba(147, 51, 234, 0.1)',
        display: 'flex',
        flexDirection: 'column',
        transition: 'width 0.3s ease',
        overflow: 'hidden'
      }}
    >
      {/* Sidebar Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: sidebarCollapsed ? 'center' : 'space-between',
          px: 2,
          py: 1,
          borderBottom: '1px solid rgba(147, 51, 234, 0.1)',
          height: 50,
          minHeight: 50,
          maxHeight: 50
        }}
      >
        {!sidebarCollapsed && (
          <Typography
            variant="h6"
            sx={{
              color: '#d8b4fe',
              fontWeight: 600,
              fontSize: '1.1rem'
            }}
          >
            SmartRAG
          </Typography>
        )}
        
        <Tooltip title={sidebarCollapsed ? 'Expand Sidebar (Ctrl+B)' : 'Collapse Sidebar (Ctrl+B)'}>
          <IconButton
            onClick={onSidebarToggle}
            aria-label={sidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
            sx={{
              color: '#94a3b8',
              '&:hover': {
                color: '#d8b4fe',
                backgroundColor: alpha('#9333ea', 0.1)
              },
              '&:focus': {
                outline: '2px solid #9333ea',
                outlineOffset: '2px',
              }
            }}
          >
            {sidebarCollapsed ? <ExpandIcon /> : <CollapseIcon />}
          </IconButton>
        </Tooltip>
      </Box>

      {/* Navigation Sections */}
      <Box sx={{ flex: 1, overflow: 'hidden' }}>
        <List sx={{ p: 0 }}>
          {sidebarSections.map((section) => {
            const isActive = sidebarSection === section.id;
            const IconComponent = section.icon;

            return (
              <ListItem key={section.id} disablePadding>
                <ListItemButton
                  onClick={() => handleSectionClick(section.id)}
                  aria-label={`Navigate to ${section.label} section`}
                  aria-current={isActive ? 'page' : undefined}
                  sx={{
                    px: sidebarCollapsed ? 1 : 2,
                    py: 0,
                    height: 50,
                    minHeight: 50,
                    maxHeight: 50,
                    backgroundColor: isActive ? alpha('#9333ea', 0.2) : 'transparent',
                    borderLeft: isActive ? '3px solid #9333ea' : '3px solid transparent',
                    '&:hover': {
                      backgroundColor: alpha('#9333ea', 0.1)
                    },
                    '&:focus': {
                      outline: '2px solid #9333ea',
                      outlineOffset: '-2px',
                    }
                  }}
                >
                  <ListItemIcon
                    sx={{
                      minWidth: sidebarCollapsed ? 'auto' : 40,
                      color: isActive ? '#d8b4fe' : '#94a3b8',
                      justifyContent: 'center'
                    }}
                  >
                    <IconComponent />
                  </ListItemIcon>

                  {!sidebarCollapsed && (
                    <ListItemText
                      primary={section.label}
                      primaryTypographyProps={{
                        sx: {
                          color: isActive ? '#d8b4fe' : '#94a3b8',
                          fontWeight: isActive ? 600 : 400,
                          fontSize: '0.9rem'
                        }
                      }}
                    />
                  )}
                </ListItemButton>
              </ListItem>
            );
          })}
        </List>
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\components\VectorizationPanel\VectorizationControls.tsx

/**
 * @file src/renderer/components/VectorizationPanel/VectorizationControls.tsx
 * @description Controls for vectorization configuration and execution
 */

import React, { useState, useCallback, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Chip,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip
} from '@mui/material';
import { ExpandMore, Memory, Settings, Refresh } from '@mui/icons-material';
import type { VectorizationConfig } from '../../../shared/types/vectorization.types';
import { validateVectorizationConfig } from '../../../shared/types/vectorization.types';
import { useOllamaModels } from '../../hooks/useOllamaModels';

interface VectorizationControlsProps {
  config: VectorizationConfig;
  onConfigChange: (config: VectorizationConfig) => void;
  onStartVectorization: () => void;
  onStopVectorization: () => void;
  isVectorizing: boolean;
  availableChunks: number;
  vectorizedChunks: number;
  disabled?: boolean;
}

const defaultModels = {
  gemini: {
    embedding: ['text-embedding-004', 'text-embedding-003'],
    llm: ['gemini-1.5-flash', 'gemini-1.5-pro']
  }
};

export const VectorizationControls: React.FC<VectorizationControlsProps> = ({
  config,
  onConfigChange,
  onStartVectorization,
  onStopVectorization,
  isVectorizing,
  availableChunks,
  vectorizedChunks,
  disabled = false
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const {
    embeddingModels,
    llmModels,
    isLoading: modelsLoading,
    error: modelsError,
    isServerRunning,
    refreshModels
  } = useOllamaModels();
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Get available models based on provider and dynamic data
  const getAvailableModels = useCallback(() => {
    if (config.provider === 'ollama') {
      return {
        embedding: embeddingModels,
        llm: llmModels
      };
    } else {
      return defaultModels.gemini;
    }
  }, [config.provider, embeddingModels, llmModels]);

  const handleConfigChange = useCallback((field: keyof VectorizationConfig, value: any) => {
    let newConfig = {
      ...config,
      [field]: value
    };

    // If provider changed, update embedding model to first available for new provider
    if (field === 'provider') {
      const newProvider = value as 'ollama' | 'gemini';
      const availableModels = newProvider === 'ollama'
        ? embeddingModels
        : defaultModels.gemini.embedding;

      // Only set embedding model if there are available models
      if (availableModels.length > 0) {
        newConfig.embeddingModel = availableModels[0];
      } else {
        // Clear embedding model if no models available
        newConfig.embeddingModel = '';
      }

      // Clear provider-specific fields when switching
      if (newProvider === 'ollama') {
        newConfig.apiKey = undefined;
        newConfig.baseUrl = newConfig.baseUrl || 'http://localhost:11434';
      } else {
        newConfig.baseUrl = undefined;
      }
    }

    // Validate the new configuration
    const validation = validateVectorizationConfig(newConfig);
    setValidationErrors(validation.errors);

    onConfigChange(newConfig);
  }, [config, onConfigChange]);

  // Update embedding model when models are loaded
  useEffect(() => {
    if (config.provider === 'ollama' && embeddingModels.length > 0 && !config.embeddingModel) {
      handleConfigChange('embeddingModel', embeddingModels[0]);
    }
  }, [embeddingModels, config.provider, config.embeddingModel, handleConfigChange]);

  const hasAvailableModels = config.provider === 'ollama'
    ? embeddingModels.length > 0
    : defaultModels.gemini.embedding.length > 0;

  const canStartVectorization = availableChunks > 0 &&
    !isVectorizing &&
    !disabled &&
    validationErrors.length === 0 &&
    hasAvailableModels &&
    config.embeddingModel.length > 0;
  const hasUnvectorizedChunks = availableChunks > vectorizedChunks;

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Memory sx={{ color: '#a855f7', mr: 1 }} />
        <Typography variant="h6" sx={{ color: '#d8b4fe', fontWeight: 600 }}>
          Vectorization Configuration
        </Typography>
      </Box>

      {/* Status Alert */}
      {availableChunks === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          No chunks available for vectorization. Please chunk some files first in the File Explorer.
        </Alert>
      ) : hasUnvectorizedChunks ? (
        <Alert severity="warning" sx={{ mb: 3 }}>
          {availableChunks - vectorizedChunks} chunks are not vectorized yet.
        </Alert>
      ) : (
        <Alert severity="success" sx={{ mb: 3 }}>
          All {vectorizedChunks} chunks are vectorized and ready for search.
        </Alert>
      )}

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
            Configuration Errors:
          </Typography>
          {validationErrors.map((error, index) => (
            <Typography key={index} variant="body2" sx={{ ml: 1 }}>
              • {error}
            </Typography>
          ))}
        </Alert>
      )}

      {/* Basic Configuration */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" sx={{ color: '#c084fc', mb: 2, fontWeight: 500 }}>
          Provider Configuration
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel sx={{ color: '#94a3b8' }}>Provider</InputLabel>
            <Select
              value={config.provider}
              label="Provider"
              onChange={(e) => handleConfigChange('provider', e.target.value)}
              disabled={isVectorizing}
              sx={{ color: '#e2e8f0' }}
            >
              <MenuItem value="ollama">Ollama (Local)</MenuItem>
              <MenuItem value="gemini">Gemini (Cloud)</MenuItem>
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel sx={{ color: '#94a3b8' }}>Embedding Model</InputLabel>
            <Select
              value={getAvailableModels().embedding.includes(config.embeddingModel) ? config.embeddingModel : ''}
              label="Embedding Model"
              onChange={(e) => handleConfigChange('embeddingModel', e.target.value)}
              disabled={isVectorizing || modelsLoading}
              sx={{ color: '#e2e8f0' }}
              endAdornment={
                config.provider === 'ollama' && (
                  <Tooltip title="Refresh Ollama models">
                    <span>
                      <IconButton
                        size="small"
                        onClick={refreshModels}
                        disabled={modelsLoading}
                        sx={{
                          position: 'absolute',
                          right: 32,
                          color: '#94a3b8',
                          '&:hover': { color: '#e2e8f0' }
                        }}
                      >
                        <Refresh fontSize="small" />
                      </IconButton>
                    </span>
                  </Tooltip>
                )
              }
            >
              {getAvailableModels().embedding.length > 0 ? (
                getAvailableModels().embedding.map((model) => (
                  <MenuItem key={model} value={model}>
                    {model}
                    {config.provider === 'ollama' && !isServerRunning && (
                      <Chip
                        label="Offline"
                        size="small"
                        color="warning"
                        sx={{ ml: 1, fontSize: '0.7rem' }}
                      />
                    )}
                  </MenuItem>
                ))
              ) : (
                <MenuItem disabled value="">
                  {config.provider === 'ollama'
                    ? 'No embedding models installed in Ollama'
                    : 'No models available'
                  }
                </MenuItem>
              )}
            </Select>
          </FormControl>
        </Box>

        {config.provider === 'ollama' && (
          <>
            <TextField
              size="small"
              label="Base URL"
              value={config.baseUrl || 'http://localhost:11434'}
              onChange={(e) => handleConfigChange('baseUrl', e.target.value)}
              disabled={isVectorizing}
              sx={{ mb: 1, minWidth: 300 }}
              InputLabelProps={{ sx: { color: '#94a3b8' } }}
              InputProps={{ sx: { color: '#e2e8f0' } }}
            />

            {/* Ollama Status Indicators */}
            <Box sx={{ mb: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
              {modelsLoading && (
                <Alert severity="info" sx={{ py: 0.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <CircularProgress size={16} />
                    Loading Ollama models...
                  </Box>
                </Alert>
              )}

              {!modelsLoading && !isServerRunning && (
                <Alert severity="error" sx={{ py: 0.5 }}>
                  Ollama server not running. Please start Ollama and install embedding models.
                </Alert>
              )}

              {!modelsLoading && isServerRunning && embeddingModels.length === 0 && (
                <Alert severity="warning" sx={{ py: 0.5 }}>
                  Ollama server running but no embedding models found. Please install embedding models (e.g., "ollama pull nomic-embed-text").
                </Alert>
              )}

              {!modelsLoading && isServerRunning && embeddingModels.length > 0 && (
                <Alert severity="success" sx={{ py: 0.5 }}>
                  Connected to Ollama. Found {embeddingModels.length} embedding models.
                </Alert>
              )}

              {modelsError && (
                <Alert severity="error" sx={{ py: 0.5 }}>
                  Error loading models: {modelsError}
                </Alert>
              )}
            </Box>
          </>
        )}

        {config.provider === 'gemini' && (
          <TextField
            size="small"
            label="API Key"
            type="password"
            value={config.apiKey || ''}
            onChange={(e) => handleConfigChange('apiKey', e.target.value)}
            disabled={isVectorizing}
            sx={{ mb: 2, minWidth: 300 }}
            InputLabelProps={{ sx: { color: '#94a3b8' } }}
            InputProps={{ sx: { color: '#e2e8f0' } }}
          />
        )}
      </Box>

      {/* Advanced Configuration */}
      <Accordion 
        expanded={showAdvanced} 
        onChange={() => setShowAdvanced(!showAdvanced)}
        sx={{ 
          backgroundColor: 'rgba(147, 51, 234, 0.05)',
          border: '1px solid rgba(147, 51, 234, 0.2)',
          mb: 3
        }}
      >
        <AccordionSummary expandIcon={<ExpandMore sx={{ color: '#94a3b8' }} />}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Settings sx={{ color: '#94a3b8', mr: 1, fontSize: 20 }} />
            <Typography sx={{ color: '#c084fc' }}>Advanced Settings</Typography>
          </Box>
        </AccordionSummary>
        <AccordionDetails>
          <TextField
            size="small"
            label="Batch Size"
            type="number"
            value={config.batchSize}
            onChange={(e) => handleConfigChange('batchSize', parseInt(e.target.value) || 10)}
            disabled={isVectorizing}
            sx={{ minWidth: 150 }}
            InputLabelProps={{ sx: { color: '#94a3b8' } }}
            InputProps={{ sx: { color: '#e2e8f0' } }}
            helperText="Number of chunks to process simultaneously"
          />
        </AccordionDetails>
      </Accordion>

      {/* Action Buttons */}
      <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
        <Button
          variant="contained"
          onClick={onStartVectorization}
          disabled={!canStartVectorization}
          sx={{
            backgroundColor: '#9333ea',
            '&:hover': { backgroundColor: '#7c3aed' },
            '&.Mui-disabled': {
              backgroundColor: 'rgba(147, 51, 234, 0.2)',
              color: 'rgba(216, 180, 254, 0.5)'
            }
          }}
        >
          {isVectorizing ? (
            <>
              <CircularProgress size={16} sx={{ mr: 1, color: 'white' }} />
              Vectorizing...
            </>
          ) : hasUnvectorizedChunks ? (
            `Vectorize ${availableChunks - vectorizedChunks} Chunks`
          ) : (
            'Re-vectorize All'
          )}
        </Button>

        {isVectorizing && (
          <Button
            variant="outlined"
            onClick={onStopVectorization}
            sx={{
              color: '#f43f5e',
              borderColor: 'rgba(244, 63, 94, 0.3)',
              '&:hover': {
                backgroundColor: 'rgba(244, 63, 94, 0.1)',
                borderColor: 'rgba(244, 63, 94, 0.5)'
              }
            }}
          >
            Stop
          </Button>
        )}

        {/* Status Chips */}
        <Box sx={{ display: 'flex', gap: 1, ml: 'auto' }}>
          <Chip
            label={`${availableChunks} chunks`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(59, 130, 246, 0.3)', color: '#60a5fa' }}
          />
          <Chip
            label={`${vectorizedChunks} vectorized`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(34, 197, 94, 0.3)', color: '#22c55e' }}
          />
        </Box>
      </Box>
    </Box>
  );
};


---

// FILE: src\renderer\components\VectorizationPanel\VectorizationErrorBoundary.tsx

/**
 * @file src/renderer/components/VectorizationPanel/VectorizationErrorBoundary.tsx
 * @description Error boundary component for vectorization panel to handle and display errors gracefully
 */

import React, { Component, ReactNode } from 'react';
import { Box, Typography, Button, Alert, Paper } from '@mui/material';
import { ErrorOutline, Refresh } from '@mui/icons-material';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

export class VectorizationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error for debugging
    console.error('[VectorizationErrorBoundary] Error caught:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Paper 
          elevation={2} 
          sx={{ 
            p: 4, 
            m: 2, 
            backgroundColor: '#1e293b',
            border: '1px solid #ef4444'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <ErrorOutline sx={{ color: '#ef4444', mr: 2, fontSize: 32 }} />
            <Typography variant="h5" sx={{ color: '#ef4444', fontWeight: 600 }}>
              Vectorization Error
            </Typography>
          </Box>

          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body1" sx={{ fontWeight: 600, mb: 1 }}>
              Something went wrong with the vectorization interface.
            </Typography>
            <Typography variant="body2" sx={{ color: '#94a3b8' }}>
              {this.state.error?.message || 'An unexpected error occurred'}
            </Typography>
          </Alert>

          {/* Error Details (Development Mode) */}
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ color: '#f1f5f9', mb: 2 }}>
                Error Details:
              </Typography>
              <Paper 
                sx={{ 
                  p: 2, 
                  backgroundColor: '#0f172a', 
                  border: '1px solid #374151',
                  maxHeight: 200,
                  overflow: 'auto'
                }}
              >
                <Typography 
                  variant="body2" 
                  component="pre" 
                  sx={{ 
                    color: '#ef4444', 
                    fontFamily: 'monospace',
                    fontSize: '0.75rem',
                    whiteSpace: 'pre-wrap'
                  }}
                >
                  {this.state.error.stack}
                </Typography>
              </Paper>
            </Box>
          )}

          {/* Recovery Actions */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<Refresh />}
              onClick={this.handleReset}
              sx={{
                backgroundColor: '#a855f7',
                '&:hover': { backgroundColor: '#9333ea' }
              }}
            >
              Try Again
            </Button>
            <Button
              variant="outlined"
              onClick={() => window.location.reload()}
              sx={{
                borderColor: '#64748b',
                color: '#e2e8f0',
                '&:hover': { borderColor: '#94a3b8', backgroundColor: 'rgba(148, 163, 184, 0.1)' }
              }}
            >
              Reload Application
            </Button>
          </Box>

          {/* Help Text */}
          <Typography variant="body2" sx={{ color: '#94a3b8', mt: 3 }}>
            If this error persists, try:
          </Typography>
          <Box component="ul" sx={{ color: '#94a3b8', mt: 1, pl: 2 }}>
            <li>Refreshing the application</li>
            <li>Checking if the project has valid chunks</li>
            <li>Verifying vectorization service configuration</li>
            <li>Restarting the application</li>
          </Box>
        </Paper>
      );
    }

    return this.props.children;
  }
}

export default VectorizationErrorBoundary;


---

// FILE: src\renderer\components\VectorizationPanel\VectorizationPanel.tsx

/**
 * @file src/renderer/components/VectorizationPanel/VectorizationPanel.tsx
 * @description Main vectorization panel component for the Dashboard tab
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Alert, Divider } from '@mui/material';
import { VectorizationControls } from './VectorizationControls';
import { VectorizationProgress } from './VectorizationProgress';
import { VectorizationResults } from './VectorizationResults';
import { VectorizationErrorBoundary } from './VectorizationErrorBoundary';
import type { Project } from '../../../shared/ipc';
import type {
  VectorizationConfig,
  VectorizationStats,
  FileVectorizationInfo
} from '../../../shared/types/vectorization.types';
import { DEFAULT_VECTORIZATION_CONFIG } from '../../../shared/types/vectorization.types';

interface VectorizationProgressItem {
  id: string;
  file: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  chunks: number;
  progress: number;
  error?: string;
  startTime?: number;
  endTime?: number;
}

interface VectorizationPanelProps {
  project: Project | null;
}

const VectorizationPanelContent: React.FC<VectorizationPanelProps> = ({ project }) => {
  // Configuration state
  const [config, setConfig] = useState<VectorizationConfig>(DEFAULT_VECTORIZATION_CONFIG);

  // Progress state
  const [isVectorizing, setIsVectorizing] = useState(false);
  const [currentFile, setCurrentFile] = useState<string>();
  const [overallProgress, setOverallProgress] = useState(0);
  const [completedFiles, setCompletedFiles] = useState(0);
  const [totalFiles, setTotalFiles] = useState(0);
  const [progressItems, setProgressItems] = useState<VectorizationProgressItem[]>([]);
  const [startTime, setStartTime] = useState<number>();

  // Results state
  const [stats, setStats] = useState<VectorizationStats | null>(null);
  const [fileInfo, setFileInfo] = useState<FileVectorizationInfo[]>([]);
  const [availableChunks, setAvailableChunks] = useState(0);
  const [vectorizedChunks, setVectorizedChunks] = useState(0);

  // Load configuration from localStorage
  useEffect(() => {
    if (project?.id) {
      const savedConfig = localStorage.getItem(`vectorization_config_${project.id}`);
      if (savedConfig) {
        try {
          const parsedConfig = JSON.parse(savedConfig);
          setConfig((prev: VectorizationConfig) => ({ ...prev, ...parsedConfig }));
        } catch (error) {
          console.error('Error loading vectorization config:', error);
        }
      }
    }
  }, [project?.id]);

  // Save configuration to localStorage
  const saveConfig = useCallback((newConfig: VectorizationConfig) => {
    if (project?.id) {
      localStorage.setItem(`vectorization_config_${project.id}`, JSON.stringify(newConfig));
    }
  }, [project?.id]);

  // Load vectorization stats and chunk information
  const loadVectorizationData = useCallback(async () => {
    if (!project?.id) return;

    try {
      const stats = await window.electronAPI.getVectorizationStats(project.id);
      const fileInfo = await window.electronAPI.getFileVectorizationInfo(project.id);

      setStats(stats);
      setAvailableChunks(stats.totalChunks);
      setVectorizedChunks(stats.vectorizedChunks);
      setFileInfo(fileInfo);
    } catch (error) {
      console.error('Error loading vectorization data:', error);
      // Set empty state on error
      setStats(null);
      setFileInfo([]);
      setAvailableChunks(0);
      setVectorizedChunks(0);
    }
  }, [project?.id]);

  // Load data when project changes
  useEffect(() => {
    loadVectorizationData();
  }, [loadVectorizationData]);

  // Subscribe to vectorization updates
  useEffect(() => {
    const unsubscribe = window.electronAPI.onVectorizationUpdate((payload) => {
      console.log('Vectorization update received:', payload);

      // Only handle updates for current project
      if (payload.projectId !== project?.id) return;

      switch (payload.type) {
        case 'progress':
          if (payload.progress !== undefined) setOverallProgress(payload.progress);
          if (payload.file) setCurrentFile(payload.file);
          if (payload.completedFiles !== undefined) setCompletedFiles(payload.completedFiles);
          if (payload.totalFiles !== undefined) setTotalFiles(payload.totalFiles);
          break;

        case 'file_complete':
          if (payload.completedFiles !== undefined) setCompletedFiles(payload.completedFiles);
          break;

        case 'batch_complete':
          setIsVectorizing(false);
          setOverallProgress(100);
          if (payload.stats) {
            setStats(payload.stats);
            setVectorizedChunks(payload.stats.vectorizedChunks);
          }
          loadVectorizationData(); // Refresh all data
          break;

        case 'error':
          setIsVectorizing(false);
          console.error('Vectorization error:', payload.error);
          break;
      }
    });

    return unsubscribe;
  }, [project?.id, loadVectorizationData]);

  // Handle configuration change
  const handleConfigChange = useCallback((newConfig: VectorizationConfig) => {
    setConfig(newConfig);
    saveConfig(newConfig);
  }, [saveConfig]);

  // Handle start vectorization
  const handleStartVectorization = useCallback(async () => {
    if (!project?.id || isVectorizing) return;

    try {
      setIsVectorizing(true);
      setStartTime(Date.now());
      setOverallProgress(0);
      setCompletedFiles(0);
      setProgressItems([]);

      const result = await window.electronAPI.startVectorization({
        projectId: project.id,
        config: config
      });

      if (!result.success) {
        throw new Error(result.message || 'Failed to start vectorization');
      }

      console.log('Vectorization started successfully');

    } catch (error) {
      console.error('Error starting vectorization:', error);
      setIsVectorizing(false);
    }
  }, [project?.id, isVectorizing, config]);

  // Handle stop vectorization
  const handleStopVectorization = useCallback(async () => {
    if (!project?.id) return;

    try {
      const result = await window.electronAPI.stopVectorization(project.id);

      if (result.success) {
        setIsVectorizing(false);
        console.log('Vectorization stopped successfully');
      } else {
        console.error('Failed to stop vectorization:', result.message);
      }
    } catch (error) {
      console.error('Error stopping vectorization:', error);
    }
  }, [project?.id]);

  // Handle test search
  const handleTestSearch = useCallback(async () => {
    if (!project?.id) return;

    try {
      const results = await window.electronAPI.testVectorSearch({
        projectId: project.id,
        query: 'test search query',
        k: 5
      });

      console.log('Vector search results:', results);
      // TODO: Show results in a dialog or panel
    } catch (error) {
      console.error('Error testing vector search:', error);
    }
  }, [project?.id]);

  // Handle clear vectors
  const handleClearVectors = useCallback(async () => {
    if (!project?.id) return;

    try {
      const result = await window.electronAPI.clearVectors(project.id);

      if (result.success) {
        setStats(null);
        setFileInfo([]);
        setVectorizedChunks(0);
        console.log('Vectors cleared successfully');
      } else {
        console.error('Failed to clear vectors:', result.message);
      }
    } catch (error) {
      console.error('Error clearing vectors:', error);
    }
  }, [project?.id]);

  if (!project) {
    return (
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        p: 4,
        textAlign: 'center'
      }}>
        <Typography variant="h6" sx={{ color: '#9ca3af', mb: 2 }}>
          No Project Selected
        </Typography>
        <Typography variant="body2" sx={{ color: '#64748b' }}>
          Please select a project to manage vectorization.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{
      p: 3,
      height: '100%',
      overflow: 'auto',
      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%)',
    }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ color: '#d8b4fe', fontWeight: 700, mb: 1 }}>
          Vectorization
        </Typography>
        <Typography variant="body1" sx={{ color: '#94a3b8' }}>
          Convert your chunked documents into searchable vector embeddings
        </Typography>
      </Box>

      {/* Project Info */}
      <Alert 
        severity="info" 
        sx={{ 
          mb: 3,
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          border: '1px solid rgba(59, 130, 246, 0.2)',
          color: '#60a5fa'
        }}
      >
        Working with project: <strong>{project.name}</strong>
      </Alert>

      {/* Controls */}
      <VectorizationControls
        config={config}
        onConfigChange={handleConfigChange}
        onStartVectorization={handleStartVectorization}
        onStopVectorization={handleStopVectorization}
        isVectorizing={isVectorizing}
        availableChunks={availableChunks}
        vectorizedChunks={vectorizedChunks}
      />

      <Divider sx={{ my: 3, borderColor: 'rgba(147, 51, 234, 0.1)' }} />

      {/* Progress */}
      {(isVectorizing || progressItems.length > 0) && (
        <>
          <VectorizationProgress
            isActive={isVectorizing}
            currentFile={currentFile}
            overallProgress={overallProgress}
            completedFiles={completedFiles}
            totalFiles={totalFiles}
            items={progressItems}
            startTime={startTime}
          />
          <Divider sx={{ my: 3, borderColor: 'rgba(147, 51, 234, 0.1)' }} />
        </>
      )}

      {/* Results */}
      <VectorizationResults
        stats={stats}
        fileInfo={fileInfo}
        onTestSearch={handleTestSearch}
        onClearVectors={handleClearVectors}
        onRefreshStats={loadVectorizationData}
      />
    </Box>
  );
};

// Export wrapped component with Error Boundary
export const VectorizationPanel: React.FC<VectorizationPanelProps> = (props) => (
  <VectorizationErrorBoundary>
    <VectorizationPanelContent {...props} />
  </VectorizationErrorBoundary>
);


---

// FILE: src\renderer\components\VectorizationPanel\VectorizationProgress.tsx

/**
 * @file src/renderer/components/VectorizationPanel/VectorizationProgress.tsx
 * @description Progress tracking component for vectorization process
 */

import React from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  Chip,
  Paper,
  Collapse
} from '@mui/material';
import { formatDistanceToNow, format } from 'date-fns';

interface VectorizationProgressItem {
  id: string;
  file: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  chunks: number;
  progress: number;
  error?: string;
  startTime?: number;
  endTime?: number;
}

interface VectorizationProgressProps {
  isActive: boolean;
  currentFile?: string;
  overallProgress: number;
  completedFiles: number;
  totalFiles: number;
  items: VectorizationProgressItem[];
  startTime?: number;
  showDetails?: boolean;
}

export const VectorizationProgress: React.FC<VectorizationProgressProps> = ({
  isActive,
  currentFile,
  overallProgress,
  completedFiles,
  totalFiles,
  items,
  startTime,
  showDetails = true
}) => {
  // Calculate elapsed time if startTime is available
  const elapsedTime = startTime ? Date.now() - startTime : null;

  // Calculate estimated remaining time based on progress
  const estimatedRemainingTime = elapsedTime && overallProgress > 0
    ? (elapsedTime / overallProgress) * (100 - overallProgress)
    : null;

  if (!isActive && items.length === 0) {
    return null;
  }

  const getStatusColor = (status: VectorizationProgressItem['status']) => {
    switch (status) {
      case 'pending': return '#64748b';
      case 'processing': return '#9333ea';
      case 'completed': return '#22c55e';
      case 'error': return '#ef4444';
      default: return '#64748b';
    }
  };

  const getStatusLabel = (status: VectorizationProgressItem['status']) => {
    switch (status) {
      case 'pending': return 'PENDING';
      case 'processing': return 'PROCESSING';
      case 'completed': return 'COMPLETED';
      case 'error': return 'ERROR';
      default: return 'UNKNOWN';
    }
  };

  return (
    <Paper
      sx={{
        p: 3,
        backgroundColor: 'rgba(147, 51, 234, 0.05)',
        border: '1px solid rgba(147, 51, 234, 0.2)',
        borderRadius: 2
      }}
    >
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" sx={{ color: '#d8b4fe', fontWeight: 600 }}>
          {isActive ? 'Vectorization in Progress' : 'Vectorization Complete'}
        </Typography>
        <Typography variant="caption" sx={{ color: '#9ca3af' }}>
          {completedFiles} / {totalFiles} files
        </Typography>
      </Box>

      {/* Overall Progress */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="body2" sx={{ color: '#c084fc', fontWeight: 500 }}>
            Overall Progress
          </Typography>
          <Typography variant="caption" sx={{ color: '#9ca3af' }}>
            {Math.round(overallProgress)}%
          </Typography>
        </Box>
        
        <LinearProgress
          variant="determinate"
          value={overallProgress}
          sx={{
            height: 8,
            borderRadius: 4,
            backgroundColor: 'rgba(147, 51, 234, 0.2)',
            '& .MuiLinearProgress-bar': {
              background: 'linear-gradient(90deg, #9333ea, #a855f7)',
              borderRadius: 4,
            }
          }}
        />

        {currentFile && (
          <Typography 
            variant="caption" 
            sx={{ 
              color: '#9ca3af', 
              mt: 1, 
              display: 'block',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap'
            }}
          >
            Current: {currentFile}
          </Typography>
        )}
      </Box>

      {/* Timing Information */}
      {startTime && (
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
          <Typography variant="caption" sx={{ color: '#9ca3af' }}>
            Started: {format(new Date(startTime), 'HH:mm:ss')}
          </Typography>
          {elapsedTime && (
            <Typography variant="caption" sx={{ color: '#9ca3af' }}>
              Elapsed: {formatDistanceToNow(new Date(Date.now() - elapsedTime), { 
                includeSeconds: true, 
                addSuffix: false 
              })}
            </Typography>
          )}
          {estimatedRemainingTime && isActive && (
            <Typography variant="caption" sx={{ color: '#9ca3af' }}>
              ETA: {formatDistanceToNow(new Date(Date.now() + estimatedRemainingTime), { 
                includeSeconds: true, 
                addSuffix: true 
              })}
            </Typography>
          )}
        </Box>
      )}

      {/* Detailed Progress List */}
      <Collapse in={showDetails && items.length > 0}>
        <Box sx={{ maxHeight: 300, overflow: 'auto' }}>
          <Typography variant="subtitle2" sx={{ color: '#c084fc', mb: 1, fontWeight: 500 }}>
            File Progress
          </Typography>
          
          <List sx={{ p: 0 }}>
            {items.map((item) => (
              <ListItem
                key={item.id}
                sx={{
                  py: 1,
                  px: 0,
                  borderBottom: '1px solid rgba(147, 51, 234, 0.1)',
                  '&:last-child': { borderBottom: 'none' }
                }}
              >
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography 
                        variant="body2" 
                        sx={{ 
                          color: '#e2e8f0',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          flex: 1
                        }}
                      >
                        {item.file}
                      </Typography>
                      
                      <Chip
                        label={getStatusLabel(item.status)}
                        size="small"
                        sx={{
                          backgroundColor: `${getStatusColor(item.status)}20`,
                          color: getStatusColor(item.status),
                          fontWeight: 600,
                          fontSize: '0.7rem',
                          minWidth: 80
                        }}
                      />
                    </Box>
                  }
                  secondary={
                    <Box sx={{ mt: 0.5 }}>
                      {item.status === 'processing' && (
                        <LinearProgress
                          variant="determinate"
                          value={item.progress}
                          sx={{
                            height: 4,
                            borderRadius: 2,
                            backgroundColor: 'rgba(147, 51, 234, 0.1)',
                            '& .MuiLinearProgress-bar': {
                              backgroundColor: '#9333ea',
                              borderRadius: 2,
                            }
                          }}
                        />
                      )}
                      
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                        <Typography variant="caption" sx={{ color: '#64748b' }}>
                          {item.chunks} chunks
                        </Typography>
                        
                        {item.status === 'processing' && (
                          <Typography variant="caption" sx={{ color: '#64748b' }}>
                            {Math.round(item.progress)}%
                          </Typography>
                        )}
                        
                        {item.status === 'completed' && item.endTime && item.startTime && (
                          <Typography variant="caption" sx={{ color: '#64748b' }}>
                            {((item.endTime - item.startTime) / 1000).toFixed(1)}s
                          </Typography>
                        )}
                        
                        {item.status === 'error' && item.error && (
                          <Typography variant="caption" sx={{ color: '#ef4444' }}>
                            {item.error}
                          </Typography>
                        )}
                      </Box>
                    </Box>
                  }
                />
              </ListItem>
            ))}
          </List>
        </Box>
      </Collapse>
    </Paper>
  );
};


---

// FILE: src\renderer\components\VectorizationPanel\VectorizationResults.tsx

/**
 * @file src/renderer/components/VectorizationPanel/VectorizationResults.tsx
 * @description Results and statistics display for vectorization process
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  Button,
  Collapse,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  Search,
  Delete,
  Refresh,
  CheckCircle,
  Info
} from '@mui/icons-material';
import type {
  VectorizationStats,
  FileVectorizationInfo
} from '../../../shared/types/vectorization.types';

interface VectorizationResultsProps {
  stats: VectorizationStats | null;
  fileInfo: FileVectorizationInfo[];
  onTestSearch?: () => void;
  onClearVectors?: () => void;
  onRefreshStats?: () => void;
  showDetails?: boolean;
}

export const VectorizationResults: React.FC<VectorizationResultsProps> = ({
  stats,
  fileInfo,
  onTestSearch,
  onClearVectors,
  onRefreshStats,
  showDetails = false
}) => {
  const [showFileDetails, setShowFileDetails] = useState(showDetails);

  if (!stats) {
    return (
      <Paper
        sx={{
          p: 3,
          backgroundColor: 'rgba(64, 64, 64, 0.05)',
          border: '1px solid rgba(64, 64, 64, 0.2)',
          borderRadius: 2,
          textAlign: 'center'
        }}
      >
        <Typography variant="body1" sx={{ color: '#9ca3af' }}>
          No vectorization results available yet.
        </Typography>
        <Typography variant="body2" sx={{ color: '#64748b', mt: 1 }}>
          Start vectorization to see statistics and results here.
        </Typography>
      </Paper>
    );
  }

  const completionPercentage = stats.totalChunks > 0 
    ? Math.round((stats.vectorizedChunks / stats.totalChunks) * 100) 
    : 0;

  const getStatusColor = (status: FileVectorizationInfo['status']) => {
    switch (status) {
      case 'complete': return '#22c55e';
      case 'partial': return '#f59e0b';
      case 'pending': return '#64748b';
      case 'error': return '#ef4444';
      default: return '#64748b';
    }
  };

  const getStatusLabel = (status: FileVectorizationInfo['status']) => {
    switch (status) {
      case 'complete': return 'COMPLETE';
      case 'partial': return 'PARTIAL';
      case 'pending': return 'PENDING';
      case 'error': return 'ERROR';
      default: return 'UNKNOWN';
    }
  };

  return (
    <Paper
      sx={{
        p: 3,
        backgroundColor: 'rgba(34, 197, 94, 0.05)',
        border: '1px solid rgba(34, 197, 94, 0.2)',
        borderRadius: 2
      }}
    >
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <CheckCircle sx={{ color: '#22c55e', mr: 1 }} />
          <Typography variant="h6" sx={{ color: '#22c55e', fontWeight: 600 }}>
            Vectorization Results
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          {onTestSearch && (
            <Tooltip title="Test vector search">
              <IconButton
                onClick={onTestSearch}
                size="small"
                sx={{ color: '#60a5fa' }}
              >
                <Search />
              </IconButton>
            </Tooltip>
          )}
          
          {onRefreshStats && (
            <Tooltip title="Refresh statistics">
              <IconButton
                onClick={onRefreshStats}
                size="small"
                sx={{ color: '#9ca3af' }}
              >
                <Refresh />
              </IconButton>
            </Tooltip>
          )}
          
          {onClearVectors && (
            <Tooltip title="Clear all vectors">
              <IconButton
                onClick={onClearVectors}
                size="small"
                sx={{ color: '#ef4444' }}
              >
                <Delete />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Summary Statistics */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" sx={{ color: '#10b981', mb: 2, fontWeight: 500 }}>
          Summary Statistics
        </Typography>
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          <Chip
            label={`${completionPercentage}% Complete`}
            size="small"
            variant="outlined"
            sx={{ 
              borderColor: 'rgba(34, 197, 94, 0.3)', 
              color: '#22c55e',
              fontWeight: 600
            }}
          />
          <Chip
            label={`${stats.vectorizedChunks}/${stats.totalChunks} chunks`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(59, 130, 246, 0.3)', color: '#60a5fa' }}
          />
          <Chip
            label={`${stats.vectorizedFiles}/${stats.totalFiles} files`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(59, 130, 246, 0.3)', color: '#60a5fa' }}
          />
          <Chip
            label={`${stats.totalVectors.toLocaleString()} vectors`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(168, 85, 247, 0.3)', color: '#a855f7' }}
          />
          <Chip
            label={`${stats.embeddingDimensions}D embeddings`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(107, 114, 128, 0.3)', color: '#9ca3af' }}
          />
        </Box>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          <Chip
            label={`${(stats.indexingTime / 1000).toFixed(2)}s total time`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(234, 179, 8, 0.3)', color: '#facc15' }}
          />
          <Chip
            label={`${(stats.averageTimePerChunk * 1000).toFixed(0)}ms/chunk`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(234, 179, 8, 0.3)', color: '#facc15' }}
          />
          <Chip
            label={`${stats.provider}:${stats.model}`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(107, 114, 128, 0.3)', color: '#9ca3af' }}
          />
        </Box>
      </Box>

      {/* File Details */}
      {fileInfo.length > 0 && (
        <Box>
          <Button
            onClick={() => setShowFileDetails(!showFileDetails)}
            startIcon={showFileDetails ? <ExpandLess /> : <ExpandMore />}
            sx={{
              color: '#10b981',
              textTransform: 'none',
              fontWeight: 500,
              mb: 2
            }}
          >
            File Details ({fileInfo.length} files)
          </Button>

          <Collapse in={showFileDetails}>
            <TableContainer 
              sx={{ 
                maxHeight: 400,
                backgroundColor: 'rgba(34, 197, 94, 0.02)',
                border: '1px solid rgba(34, 197, 94, 0.1)',
                borderRadius: 1
              }}
            >
              <Table size="small" stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ color: '#10b981', fontWeight: 600 }}>File</TableCell>
                    <TableCell sx={{ color: '#10b981', fontWeight: 600 }}>Status</TableCell>
                    <TableCell sx={{ color: '#10b981', fontWeight: 600 }}>Chunks</TableCell>
                    <TableCell sx={{ color: '#10b981', fontWeight: 600 }}>Vectors</TableCell>
                    <TableCell sx={{ color: '#10b981', fontWeight: 600 }}>Last Updated</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {fileInfo.map((file, index) => (
                    <TableRow key={index}>
                      <TableCell sx={{ color: '#e2e8f0', maxWidth: 200 }}>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {file.file}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatusLabel(file.status)}
                          size="small"
                          sx={{
                            backgroundColor: `${getStatusColor(file.status)}20`,
                            color: getStatusColor(file.status),
                            fontWeight: 600,
                            fontSize: '0.7rem'
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ color: '#94a3b8' }}>
                        {file.vectorizedChunks}/{file.chunks}
                      </TableCell>
                      <TableCell sx={{ color: '#94a3b8' }}>
                        {file.vectors.toLocaleString()}
                      </TableCell>
                      <TableCell sx={{ color: '#94a3b8' }}>
                        {file.lastVectorized 
                          ? new Date(file.lastVectorized).toLocaleString()
                          : 'Never'
                        }
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Collapse>
        </Box>
      )}

      {/* Last Updated */}
      <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid rgba(34, 197, 94, 0.1)' }}>
        <Typography variant="caption" sx={{ color: '#64748b', display: 'flex', alignItems: 'center' }}>
          <Info sx={{ fontSize: 14, mr: 0.5 }} />
          Last updated: {new Date(stats.lastUpdated).toLocaleString()}
        </Typography>
      </Box>
    </Paper>
  );
};


---

// FILE: src\renderer\hooks\useOllamaModels.ts

/**
 * @file src/renderer/hooks/useOllamaModels.ts
 * @description React hook for fetching and managing Ollama models.
 */

import { useState, useEffect, useCallback } from 'react';

export interface OllamaModel {
  name: string;
  model: string;
  modified_at: string;
  size: number;
  digest: string;
  details?: {
    parent_model?: string;
    format?: string;
    family?: string;
    families?: string[];
    parameter_size?: string;
    quantization_level?: string;
  };
}

export interface OllamaModelsState {
  allModels: OllamaModel[];
  embeddingModels: string[];
  llmModels: string[];
  isLoading: boolean;
  error: string | null;
  isServerRunning: boolean;
}

export interface UseOllamaModelsReturn extends OllamaModelsState {
  refreshModels: () => Promise<void>;
  checkServer: () => Promise<void>;
}

export const useOllamaModels = (): UseOllamaModelsReturn => {
  const [state, setState] = useState<OllamaModelsState>({
    allModels: [],
    embeddingModels: [],
    llmModels: [],
    isLoading: false,
    error: null,
    isServerRunning: false,
  });

  const checkServer = useCallback(async () => {
    try {
      const result = await window.electronAPI.invoke('ollama:checkServer');
      setState(prev => ({
        ...prev,
        isServerRunning: result.isRunning,
        error: result.success ? null : result.error
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isServerRunning: false,
        error: error instanceof Error ? error.message : 'Failed to check server'
      }));
    }
  }, []);

  const refreshModels = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Check server first
      await checkServer();

      // Fetch all models in parallel
      const [allModelsResult, embeddingModelsResult, llmModelsResult] = await Promise.all([
        window.electronAPI.invoke('ollama:getModels'),
        window.electronAPI.invoke('ollama:getEmbeddingModels'),
        window.electronAPI.invoke('ollama:getLLMModels'),
      ]);

      setState(prev => ({
        ...prev,
        allModels: allModelsResult.models || [],
        embeddingModels: embeddingModelsResult.models || [],
        llmModels: llmModelsResult.models || [],
        isLoading: false,
        error: null
      }));

    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch models'
      }));
    }
  }, [checkServer]);

  // Initial load
  useEffect(() => {
    refreshModels();
  }, [refreshModels]);

  return {
    ...state,
    refreshModels,
    checkServer,
  };
};


---

// FILE: src\renderer\main.tsx

/**
 * @file src/renderer/main.tsx
 * @description Main entry point for the React application in the renderer process.
 */

import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App.tsx';
// import TestApp from './TestApp.tsx';
import './index.css';

console.log('[main.tsx] Starting React application...');
console.log('[main.tsx] Document ready state:', document.readyState);
console.log('[main.tsx] Window object:', window);
console.log('[main.tsx] ElectronAPI available:', !!window.electronAPI);

const rootElement = document.getElementById('root');
if (!rootElement) {
  console.error('[main.tsx] Root element not found!');
  document.body.innerHTML = '<div style="color: red; font-size: 20px; padding: 20px;">ERROR: Root element not found!</div>';
} else {
  console.log('[main.tsx] Root element found, creating React root...');
  console.log('[main.tsx] Root element:', rootElement);
  
  try {
    const root = ReactDOM.createRoot(rootElement);
    console.log('[main.tsx] React root created successfully');
    
    root.render(
      <React.StrictMode>
        <App />
      </React.StrictMode>
    );
    console.log('[main.tsx] React app rendered successfully!');
    
    // Добавим проверку через небольшую задержку
    setTimeout(() => {
      console.log('[main.tsx] Root element content after render:', rootElement.innerHTML.substring(0, 200));
    }, 1000);
    
  } catch (error) {
    console.error('[main.tsx] Error rendering React app:', error);
    console.error('[main.tsx] Error stack:', error instanceof Error ? error.stack : 'Unknown error');
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    document.body.innerHTML = `<div style="color: red; font-size: 16px; padding: 20px;">RENDER ERROR: ${errorMessage}</div>`;
  }
}


---

// FILE: src\renderer\preload.ts

/**
 * @file src/renderer/preload.ts
 * @description Preload script for the renderer process.
 * Exposes Node.js APIs to the renderer process in a secure way using contextBridge.
 */

import { contextBridge, ipcRenderer } from 'electron';
import type { IpcApi, IndexingUpdatePayload } from '../shared/ipc.d';

const ipcApi: IpcApi = {
  // Indexing
  startIndexing: (payload: { filePath: string; projectId: number }) => {
    ipcRenderer.send('start-indexing', payload);
  },
  onIndexingUpdate: (callback: (payload: IndexingUpdatePayload) => void) => {
    const listener = (event: any, payload: IndexingUpdatePayload) => callback(payload);
    ipcRenderer.on('indexing-update', listener);
    return () => {
      ipcRenderer.removeListener('indexing-update', listener);
    };
  },

  // LangChain RAG - индексация выбранных файлов
  indexSelectedFiles: (payload: { filePaths: string[]; projectId: number; }) => {
    return ipcRenderer.invoke('langchain:indexSelectedFiles', payload);
  },

  // LangChain indexing updates
  onLangChainIndexingUpdate: (callback: (payload: any) => void) => {
    const listener = (_event: any, payload: any) => callback(payload);
    ipcRenderer.on('langchain-indexing-update', listener);
    return () => {
      ipcRenderer.removeListener('langchain-indexing-update', listener);
    };
  },

  // LangChain RAG - отмена индексации
  cancelIndexing: () => ipcRenderer.invoke('langchain:cancelIndexing'),

  // LangChain RAG - очистка chunks проекта
  clearProjectChunks: (projectId: number) => ipcRenderer.invoke('langchain:clearProjectChunks', { projectId }),

  // Vectorization
  startVectorization: (payload: { projectId: number; config: any }) =>
    ipcRenderer.invoke('vectorization:start', payload),
  stopVectorization: (projectId: number) =>
    ipcRenderer.invoke('vectorization:stop', projectId),
  getVectorizationStats: (projectId: number) =>
    ipcRenderer.invoke('vectorization:getStats', projectId),
  getFileVectorizationInfo: (projectId: number) =>
    ipcRenderer.invoke('vectorization:getFileInfo', projectId),
  clearVectors: (projectId: number) =>
    ipcRenderer.invoke('vectorization:clear', projectId),
  testVectorSearch: (payload: { projectId: number; query: string; k?: number }) =>
    ipcRenderer.invoke('vectorization:testSearch', payload),
  onVectorizationUpdate: (callback: (payload: any) => void) => {
    const listener = (_event: any, payload: any) => callback(payload);
    ipcRenderer.on('vectorization-update', listener);
    return () => {
      ipcRenderer.removeListener('vectorization-update', listener);
    };
  },

  // Projects
  getAllProjects: () => ipcRenderer.invoke('projects:getAll'),
  addProject: (path: string) => ipcRenderer.invoke('projects:add', path),
  deleteProject: (id: number) => ipcRenderer.invoke('projects:delete', id),

  // Project Files
  addSelectedFiles: (payload: { filePaths: string[]; projectId: number; copyFiles?: boolean }) =>
    ipcRenderer.invoke('projectFiles:addSelected', payload),
  getSelectedFiles: (projectId: number) => ipcRenderer.invoke('projectFiles:getSelected', projectId),
  removeSelectedFile: (projectId: number, fileId: string) =>
    ipcRenderer.invoke('projectFiles:removeSelected', projectId, fileId),
  clearSelectedFiles: (projectId: number) => ipcRenderer.invoke('projectFiles:clearSelected', projectId),

  // FS
  openDirectoryDialog: () => ipcRenderer.invoke('dialog:openDirectory'),
  readDirectory: (path: string) => ipcRenderer.invoke('fs:readDirectory', path),
  readDirectoryLazy: (path: string) => ipcRenderer.invoke('fs:readDirectoryLazy', path),
  readFile: (filePath: string) => ipcRenderer.invoke('fs:readFile', filePath),
  getTokenCount: (filePath: string) => ipcRenderer.invoke('fs:getTokenCount', filePath),

  // App Data
  clearAppData: () => ipcRenderer.invoke('app:clearData'),

  // Generic invoke method for dynamic IPC calls
  invoke: (channel: string, ...args: any[]) => ipcRenderer.invoke(channel, ...args),
};

contextBridge.exposeInMainWorld('electronAPI', ipcApi);


---

// FILE: src\shared\hooks\useGitignore.ts

/**
 * @file src/shared/hooks/useGitignore.ts
 * @description Hook for managing gitignore patterns
 */

import { useEffect } from 'react';
import { useAppStore } from '../store';
import { readGitignoreFile } from '../utils/fileFilters';

export const useGitignore = (projectPath?: string) => {
  const { 
    autoSelectFilters, 
    gitignorePatterns, 
    setGitignorePatterns 
  } = useAppStore();

  // Load gitignore patterns when project or settings change
  useEffect(() => {
    const loadGitignorePatterns = async () => {
      if (
        projectPath &&
        autoSelectFilters?.gitignore?.enabled &&
        autoSelectFilters?.gitignore?.autoLoad
      ) {
        try {
          const patterns = await readGitignoreFile(projectPath);
          setGitignorePatterns(patterns);
        } catch (error) {
          console.error('Error loading gitignore patterns:', error);
          setGitignorePatterns([]);
        }
      } else {
        setGitignorePatterns([]);
      }
    };

    loadGitignorePatterns();
  }, [
    projectPath,
    autoSelectFilters?.gitignore?.enabled,
    autoSelectFilters?.gitignore?.autoLoad,
    setGitignorePatterns
  ]);

  return {
    gitignorePatterns,
    isGitignoreEnabled: autoSelectFilters?.gitignore?.enabled ?? false,
    isAutoLoadEnabled: autoSelectFilters?.gitignore?.autoLoad ?? false,
    setGitignorePatterns
  };
};


---

// FILE: src\shared\store.ts

/**
 * @file store.ts
 * @description Zustand store for global application state management
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { AutoSelectFilters } from './types/filters.types';
import { DEFAULT_AUTO_SELECT_FILTERS } from './types/filters.types';

export interface Project {
  id: string;
  name: string;
  path: string;
  dataPath?: string;
  createdAt: string;
  lastOpened?: string;
}

export interface VectorizationState {
  isVectorizing: boolean;
  progress: number;
  currentFile?: string;
  completedFiles: number;
  totalFiles: number;
  stats?: any;
  fileInfo: any[];
  config?: any;
}

export interface ProjectTab {
  id: string;
  project: Project | null;
  selectedFiles: Set<string>;
  selectedFilePaths: string[];
  totalSelectedSize: number;
  totalSelectedTokens: number;
  isChunked: boolean;
  chunkingResults?: any;
  vectorization: VectorizationState;
}

export interface AppState {
  // Navigation state
  activePanel: string;
  activeTabId: string | null;
  tabs: ProjectTab[];

  // Project state
  selectedProject: Project | null;
  openProjects: Project[];

  // File selection state
  selectedFiles: Set<string>;
  selectedFilePaths: string[];
  totalSelectedSize: number;
  totalSelectedTokens: number;

  // Chunking state
  isChunked: boolean;
  chunkingResults: any;

  // Vectorization state
  vectorization: VectorizationState;

  // Auto-select filters
  autoSelectFilters: AutoSelectFilters;
  gitignorePatterns: string[];
  
  // Actions
  setActivePanel: (panel: string) => void;
  setActiveTab: (tabId: string | null) => void;
  addTab: (project: Project) => void;
  removeTab: (tabId: string) => void;
  updateTab: (tabId: string, updates: Partial<ProjectTab>) => void;
  
  setSelectedProject: (project: Project | null) => void;
  addOpenProject: (project: Project) => void;
  removeOpenProject: (projectId: string) => void;
  
  setSelectedFiles: (files: Set<string>) => void;
  setSelectedFilePaths: (paths: string[]) => void;
  setTotalSelectedSize: (size: number) => void;
  setTotalSelectedTokens: (tokens: number) => void;
  
  setChunkingResults: (results: any) => void;
  setIsChunked: (chunked: boolean) => void;

  // Vectorization actions
  setVectorizationState: (state: Partial<VectorizationState>) => void;
  updateVectorization: (tabId: string, updates: Partial<VectorizationState>) => void;

  // Utility actions
  clearSelection: () => void;
  resetState: () => void;

  // Filter actions
  setAutoSelectFilters: (filters: AutoSelectFilters) => void;
  updateAutoSelectFilters: (updates: Partial<AutoSelectFilters>) => void;
  setGitignorePatterns: (patterns: string[]) => void;
}

const generateTabId = () => `tab-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // Initial state
      activePanel: 'projects',
      activeTabId: null,
      tabs: [],
      
      selectedProject: null,
      openProjects: [],
      
      selectedFiles: new Set(),
      selectedFilePaths: [],
      totalSelectedSize: 0,
      totalSelectedTokens: 0,
      
      isChunked: false,
      chunkingResults: null,

      // Vectorization state
      vectorization: {
        isVectorizing: false,
        progress: 0,
        completedFiles: 0,
        totalFiles: 0,
        fileInfo: []
      },

      // Auto-select filters with defaults
      autoSelectFilters: DEFAULT_AUTO_SELECT_FILTERS,
      gitignorePatterns: [],
      
      // Navigation actions
      setActivePanel: (panel) => set({ activePanel: panel }),
      
      setActiveTab: (tabId) => set({ activeTabId: tabId }),
      
      addTab: (project) => {
        const state = get();
        const existingTab = state.tabs.find(tab => tab.project?.id === project.id);
        
        if (existingTab) {
          set({ activeTabId: existingTab.id });
          return;
        }
        
        const newTab: ProjectTab = {
          id: generateTabId(),
          project,
          selectedFiles: new Set(),
          selectedFilePaths: [],
          totalSelectedSize: 0,
          totalSelectedTokens: 0,
          isChunked: false,
          chunkingResults: null,
          vectorization: {
            isVectorizing: false,
            progress: 0,
            completedFiles: 0,
            totalFiles: 0,
            fileInfo: []
          }
        };
        
        set({
          tabs: [...state.tabs, newTab],
          activeTabId: newTab.id,
          selectedProject: project
        });
      },
      
      removeTab: (tabId) => {
        const state = get();
        const newTabs = state.tabs.filter(tab => tab.id !== tabId);
        const newActiveTabId = state.activeTabId === tabId 
          ? (newTabs.length > 0 ? newTabs[newTabs.length - 1].id : null)
          : state.activeTabId;
          
        set({
          tabs: newTabs,
          activeTabId: newActiveTabId,
          selectedProject: newActiveTabId 
            ? newTabs.find(tab => tab.id === newActiveTabId)?.project || null
            : null
        });
      },
      
      updateTab: (tabId, updates) => {
        const state = get();
        const newTabs = state.tabs.map(tab =>
          tab.id === tabId ? { ...tab, ...updates } : tab
        );
        
        set({ tabs: newTabs });
        
        // Update current state if this is the active tab
        if (state.activeTabId === tabId) {
          const updatedTab = newTabs.find(tab => tab.id === tabId);
          if (updatedTab) {
            set({
              selectedFiles: updatedTab.selectedFiles,
              selectedFilePaths: updatedTab.selectedFilePaths,
              totalSelectedSize: updatedTab.totalSelectedSize,
              totalSelectedTokens: updatedTab.totalSelectedTokens,
              isChunked: updatedTab.isChunked,
              chunkingResults: updatedTab.chunkingResults,
              vectorization: updatedTab.vectorization
            });
          }
        }
      },
      
      // Project actions
      setSelectedProject: (project) => set({ selectedProject: project }),
      
      addOpenProject: (project) => {
        const state = get();
        if (!state.openProjects.find(p => p.id === project.id)) {
          set({ openProjects: [...state.openProjects, project] });
        }
      },
      
      removeOpenProject: (projectId) => {
        const state = get();
        set({ openProjects: state.openProjects.filter(p => p.id !== projectId) });
      },
      
      // File selection actions
      setSelectedFiles: (files) => {
        set({ selectedFiles: files });
        
        // Update active tab
        const state = get();
        if (state.activeTabId) {
          get().updateTab(state.activeTabId, { selectedFiles: files });
        }
      },
      
      setSelectedFilePaths: (paths) => {
        set({ selectedFilePaths: paths });
        
        // Update active tab
        const state = get();
        if (state.activeTabId) {
          get().updateTab(state.activeTabId, { selectedFilePaths: paths });
        }
      },
      
      setTotalSelectedSize: (size) => {
        set({ totalSelectedSize: size });
        
        // Update active tab
        const state = get();
        if (state.activeTabId) {
          get().updateTab(state.activeTabId, { totalSelectedSize: size });
        }
      },
      
      setTotalSelectedTokens: (tokens) => {
        set({ totalSelectedTokens: tokens });
        
        // Update active tab
        const state = get();
        if (state.activeTabId) {
          get().updateTab(state.activeTabId, { totalSelectedTokens: tokens });
        }
      },
      
      // Chunking actions
      setChunkingResults: (results) => {
        set({ chunkingResults: results });
        
        // Update active tab
        const state = get();
        if (state.activeTabId) {
          get().updateTab(state.activeTabId, { chunkingResults: results });
        }
      },
      
      setIsChunked: (chunked) => {
        set({ isChunked: chunked });

        // Update active tab
        const state = get();
        if (state.activeTabId) {
          get().updateTab(state.activeTabId, { isChunked: chunked });
        }
      },

      // Vectorization actions
      setVectorizationState: (updates) => {
        const state = get();
        set({
          vectorization: { ...state.vectorization, ...updates }
        });

        // Update active tab
        if (state.activeTabId) {
          get().updateTab(state.activeTabId, {
            vectorization: { ...state.vectorization, ...updates }
          });
        }
      },

      updateVectorization: (tabId, updates) => {
        const state = get();
        const newTabs = state.tabs.map(tab =>
          tab.id === tabId
            ? { ...tab, vectorization: { ...tab.vectorization, ...updates } }
            : tab
        );

        set({ tabs: newTabs });

        // Update current state if this is the active tab
        if (state.activeTabId === tabId) {
          const updatedTab = newTabs.find(tab => tab.id === tabId);
          if (updatedTab) {
            set({ vectorization: updatedTab.vectorization });
          }
        }
      },
      
      // Utility actions
      clearSelection: () => {
        set({
          selectedFiles: new Set(),
          selectedFilePaths: [],
          totalSelectedSize: 0,
          totalSelectedTokens: 0
        });
        
        // Update active tab
        const state = get();
        if (state.activeTabId) {
          get().updateTab(state.activeTabId, {
            selectedFiles: new Set(),
            selectedFilePaths: [],
            totalSelectedSize: 0,
            totalSelectedTokens: 0
          });
        }
      },
      
      resetState: () => set({
        activePanel: 'projects',
        activeTabId: null,
        tabs: [],
        selectedProject: null,
        openProjects: [],
        selectedFiles: new Set(),
        selectedFilePaths: [],
        totalSelectedSize: 0,
        totalSelectedTokens: 0,
        isChunked: false,
        chunkingResults: null,
        vectorization: {
          isVectorizing: false,
          progress: 0,
          completedFiles: 0,
          totalFiles: 0,
          fileInfo: []
        },
        autoSelectFilters: DEFAULT_AUTO_SELECT_FILTERS,
        gitignorePatterns: []
      }),

      // Filter actions
      setAutoSelectFilters: (filters) => set({ autoSelectFilters: filters }),
      setGitignorePatterns: (patterns) => set({ gitignorePatterns: patterns }),

      updateAutoSelectFilters: (updates) => {
        const state = get();
        set({
          autoSelectFilters: {
            ...state.autoSelectFilters,
            ...updates,
            fileTypes: {
              ...state.autoSelectFilters.fileTypes,
              ...updates.fileTypes
            },
            directories: {
              ...state.autoSelectFilters.directories,
              ...updates.directories
            }
          }
        });
      }
    }),
    {
      name: 'smartrag-store',
      partialize: (state) => ({
        activePanel: state.activePanel,
        activeTabId: state.activeTabId,
        tabs: state.tabs.map(tab => ({
          ...tab,
          selectedFiles: Array.from(tab.selectedFiles) // Convert Set to Array for serialization
        })),
        selectedProject: state.selectedProject,
        openProjects: state.openProjects,
        autoSelectFilters: state.autoSelectFilters
      }),
      onRehydrateStorage: () => (state) => {
        if (state?.tabs) {
          // Convert Arrays back to Sets after rehydration
          state.tabs = state.tabs.map(tab => ({
            ...tab,
            selectedFiles: new Set(tab.selectedFiles as any)
          }));
          
          // Restore current state from active tab
          if (state.activeTabId) {
            const activeTab = state.tabs.find(tab => tab.id === state.activeTabId);
            if (activeTab) {
              state.selectedFiles = activeTab.selectedFiles;
              state.selectedFilePaths = activeTab.selectedFilePaths;
              state.totalSelectedSize = activeTab.totalSelectedSize;
              state.totalSelectedTokens = activeTab.totalSelectedTokens;
              state.isChunked = activeTab.isChunked;
              state.chunkingResults = activeTab.chunkingResults;
              state.vectorization = activeTab.vectorization || {
                isVectorizing: false,
                progress: 0,
                completedFiles: 0,
                totalFiles: 0,
                fileInfo: []
              };
            }
          }
        }
      }
    }
  )
);


---

// FILE: src\shared\types\filters.types.ts

/**
 * @file src/shared/types/filters.types.ts
 * @description Type definitions for auto-select filters configuration
 */

/**
 * Configuration for file type filtering in auto-select
 */
export interface FileTypeFilter {
  /** Whether this filter category is enabled */
  enabled: boolean;
  /** File extensions to filter (with or without leading dot) */
  extensions: string[];
  /** Additional patterns to match */
  patterns?: string[];
}

/**
 * Configuration for directory filtering in auto-select
 */
export interface DirectoryFilter {
  /** Whether to exclude this directory type */
  enabled: boolean;
  /** Directory names or patterns to match */
  patterns: string[];
}

/**
 * Complete auto-select filters configuration
 */
export interface AutoSelectFilters {
  /** File type filters organized by category */
  fileTypes: {
    /** Image files (png, jpg, jpeg, gif, webp, ico) */
    images: FileTypeFilter;
    /** Vector graphics and icons (svg) */
    vectors: FileTypeFilter;
    /** Log files and temporary files */
    logs: FileTypeFilter;
    /** Icon directories and files */
    icons: FileTypeFilter;
    /** Database and binary files */
    binary: FileTypeFilter;
    /** Lock files (package-lock.json, yarn.lock, etc.) */
    lockFiles: FileTypeFilter;
  };
  
  /** Directory filters */
  directories: {
    /** Node.js dependencies */
    nodeModules: DirectoryFilter;
    /** Build output directories */
    buildOutputs: DirectoryFilter;
    /** Version control directories */
    versionControl: DirectoryFilter;
    /** IDE and editor directories */
    ideFiles: DirectoryFilter;
  };
  
  /** Hidden files (starting with dot) */
  hiddenFiles: {
    enabled: boolean;
  };

  /** Gitignore integration */
  gitignore: {
    enabled: boolean;
    autoLoad: boolean; // Automatically load .gitignore from project root
  };

  /** Custom gitignore patterns */
  customPatterns: {
    enabled: boolean;
    patterns: string[];
  };
}

/**
 * Default auto-select filters configuration
 */
export const DEFAULT_AUTO_SELECT_FILTERS: AutoSelectFilters = {
  fileTypes: {
    images: {
      enabled: true,
      extensions: ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.bmp', '.tiff'],
      patterns: []
    },
    vectors: {
      enabled: false, // Don't exclude SVG by default - many are used for icons but some for graphics
      extensions: ['.svg'],
      patterns: []
    },
    logs: {
      enabled: true,
      extensions: ['.log', '.tmp', '.temp'],
      patterns: ['*.log.*', '*.tmp.*']
    },
    icons: {
      enabled: true,
      extensions: ['.ico'], // Only ICO files, not SVG/PNG which might be used for other purposes
      patterns: ['**/icons/**', '**/icon/**', 'favicon.*']
    },
    binary: {
      enabled: true,
      extensions: ['.db', '.sqlite', '.sqlite3', '.gpg', '.bin', '.exe', '.dll'],
      patterns: []
    },
    lockFiles: {
      enabled: true,
      extensions: ['.lock'],
      patterns: ['*-lock.*', 'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml']
    }
  },

  directories: {
    nodeModules: {
      enabled: true,
      patterns: ['node_modules'] // Only exact match, not nested patterns
    },
    buildOutputs: {
      enabled: true,
      patterns: ['dist', 'build', 'out', 'target', 'bin', 'obj', '.next', '.nuxt']
    },
    versionControl: {
      enabled: true,
      patterns: ['.git', '.svn', '.hg', '.bzr']
    },
    ideFiles: {
      enabled: true,
      patterns: ['.vscode', '.idea', '.vs', '__pycache__', '.pytest_cache']
    }
  },

  hiddenFiles: {
    enabled: false // Don't exclude hidden files by default - some are important config files
  },

  gitignore: {
    enabled: true, // Enable gitignore by default
    autoLoad: true // Automatically load .gitignore from project root
  },

  customPatterns: {
    enabled: true,
    patterns: []
  }
};

/**
 * Filter preset configurations for quick setup
 */
export interface FilterPreset {
  id: string;
  name: string;
  description: string;
  filters: AutoSelectFilters;
}

export const FILTER_PRESETS: FilterPreset[] = [
  {
    id: 'strict',
    name: 'Strict Filtering',
    description: 'Excludes all non-code files, images, logs, and build artifacts',
    filters: DEFAULT_AUTO_SELECT_FILTERS
  },
  {
    id: 'moderate',
    name: 'Moderate Filtering',
    description: 'Excludes images and logs but includes some config files',
    filters: {
      ...DEFAULT_AUTO_SELECT_FILTERS,
      fileTypes: {
        ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes,
        vectors: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.vectors, enabled: false },
        lockFiles: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.lockFiles, enabled: false }
      }
    }
  },
  {
    id: 'minimal',
    name: 'Minimal Filtering',
    description: 'Only excludes obvious non-code files like node_modules and build outputs',
    filters: {
      ...DEFAULT_AUTO_SELECT_FILTERS,
      fileTypes: {
        images: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.images, enabled: false },
        vectors: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.vectors, enabled: false },
        logs: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.logs, enabled: false },
        icons: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.icons, enabled: false },
        binary: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.binary, enabled: true },
        lockFiles: { ...DEFAULT_AUTO_SELECT_FILTERS.fileTypes.lockFiles, enabled: false }
      },
      hiddenFiles: { enabled: false },
      gitignore: { ...DEFAULT_AUTO_SELECT_FILTERS.gitignore, enabled: true }
    }
  }
];


---

// FILE: src\shared\types\vectorization.types.ts

/**
 * @file src/shared/types/vectorization.types.ts
 * @description Type definitions for vectorization functionality - STUB VERSION
 * Vectorization functionality has been removed, these are placeholder types.
 */

export interface VectorizationConfig {
  provider: string;
  model: string;
  embeddingModel: string;
  baseUrl?: string;
  apiKey?: string;
  batchSize?: number;
}

export interface VectorizationStats {
  totalChunks: number;
  vectorizedChunks: number;
  totalFiles: number;
  vectorizedFiles: number;
  totalVectors: number;
  embeddingDimensions: number;
  indexingTime: number;
  averageTimePerChunk: number;
  provider: string;
  model: string;
  lastUpdated: number;
}

export interface FileVectorizationInfo {
  file: string;
  chunks: number;
  vectorizedChunks: number;
  vectors: number;
  status: 'pending' | 'partial' | 'complete';
}

export interface VectorizationUpdatePayload {
  type: 'progress' | 'file_complete' | 'batch_complete' | 'error';
  projectId: number;
  file?: string;
  progress?: number;
  completedFiles?: number;
  totalFiles?: number;
  stats?: VectorizationStats;
  error?: string;
}

export interface ChunkData {
  id: string;
  content: string;
  metadata: {
    file: string;
    chunkIndex: number;
    startLine?: number;
    endLine?: number;
    [key: string]: any;
  };
}

/**
 * Default vectorization configuration
 */
export const DEFAULT_VECTORIZATION_CONFIG: VectorizationConfig = {
  provider: 'ollama',
  model: 'llama3.2',
  embeddingModel: 'nomic-embed-text',
  baseUrl: 'http://localhost:11434',
  batchSize: 10
};

/**
 * Validate vectorization configuration - STUB IMPLEMENTATION
 */
export function validateVectorizationConfig(_config: Partial<VectorizationConfig>): {
  isValid: boolean;
  errors: string[];
} {
  return {
    isValid: false,
    errors: ['Vectorization functionality has been removed']
  };
}


---

// FILE: src\shared\utils\fileFilters.ts

/**
 * @file src/shared/utils/fileFilters.ts
 * @description Centralized file filtering utilities for auto-select functionality
 */

import type { AutoSelectFilters } from '../types/filters.types';

/**
 * Parse gitignore patterns from file content
 */
export const parseGitignorePatterns = (content: string): string[] => {
  return content
    .split('\n')
    .map(line => line.trim())
    .filter(line => line && !line.startsWith('#')) // Remove empty lines and comments
    .filter(line => !line.startsWith('!')); // Remove negation patterns for simplicity
};

/**
 * Check if a file/directory matches gitignore patterns
 * Simplified but correct gitignore implementation for auto-select
 */
const matchesGitignorePattern = (name: string, fullPath: string, pattern: string): boolean => {
  // Skip empty patterns
  if (!pattern.trim()) return false;

  const originalPattern = pattern.trim();

  // Handle directory patterns (ending with /)
  if (originalPattern.endsWith('/')) {
    // Directory patterns only match directories, not files
    // For auto-select, we assume isLeaf=false means directory
    const dirPattern = originalPattern.slice(0, -1);
    return matchesPattern(name, dirPattern) || matchesPattern(fullPath, dirPattern);
  }

  // Handle absolute paths (starting with /)
  if (originalPattern.startsWith('/')) {
    const pathPattern = originalPattern.slice(1);
    return matchesPattern(fullPath, pathPattern);
  }

  // For other patterns, match against filename only
  return matchesPattern(name, originalPattern);
};

/**
 * Helper function to match a string against a pattern with wildcards
 */
const matchesPattern = (str: string, pattern: string): boolean => {
  // Convert gitignore pattern to regex
  // Escape special regex characters except * and ?
  let regexPattern = pattern
    .replace(/[.+^${}()|[\]\\]/g, '\\$&') // Escape special chars
    .replace(/\*/g, '.*')  // * matches any sequence of characters
    .replace(/\?/g, '.');  // ? matches any single character

  const regex = new RegExp(`^${regexPattern}$`, 'i');
  return regex.test(str);
};

/**
 * Check if a file matches file type filters
 */
const matchesFileTypeFilters = (name: string, fullPath: string, filters: AutoSelectFilters): boolean => {
  const lowerName = name.toLowerCase();
  const { fileTypes } = filters;

  // Images - exact extension match
  if (fileTypes.images.enabled &&
      fileTypes.images.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
    return true;
  }

  // Vector graphics - exact extension match
  if (fileTypes.vectors.enabled &&
      fileTypes.vectors.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
    return true;
  }

  // Log files - extension and pattern match
  if (fileTypes.logs.enabled) {
    if (fileTypes.logs.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
      return true;
    }
    if (fileTypes.logs.patterns?.some(pattern => {
      if (pattern.includes('*')) {
        const regexPattern = pattern.replace(/\*/g, '.*');
        const regex = new RegExp(`^${regexPattern}$`, 'i');
        return regex.test(name);
      }
      return false;
    })) {
      return true;
    }
  }

  // Icon files - extension and path-based patterns
  if (fileTypes.icons.enabled) {
    // Check if it's an icon file by extension
    if (fileTypes.icons.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
      return true;
    }

    // Check path-based patterns for icon directories
    if (fileTypes.icons.patterns?.some(pattern => {
      if (pattern.includes('**/')) {
        // Path-based pattern like **/icons/** - check if any path segment matches exactly
        const pathSegment = pattern.replace(/\*\*/g, '').replace(/\*/g, '').replace(/\//g, '');
        const pathSegments = fullPath.toLowerCase().split('/');
        return pathSegments.some(segment => segment === pathSegment.toLowerCase());
      } else if (pattern.includes('*')) {
        // File pattern like favicon.*
        const regexPattern = pattern.replace(/\*/g, '.*');
        const regex = new RegExp(`^${regexPattern}$`, 'i');
        return regex.test(name);
      }
      return false;
    })) {
      return true;
    }

    // Only check for exact favicon files, not any file containing "icon"
    if (lowerName.startsWith('favicon.')) {
      return true;
    }
  }

  // Binary files - exact extension match
  if (fileTypes.binary.enabled &&
      fileTypes.binary.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
    return true;
  }

  // Lock files - extension and exact pattern match
  if (fileTypes.lockFiles.enabled) {
    if (fileTypes.lockFiles.extensions.some(ext => lowerName.endsWith(ext.toLowerCase()))) {
      return true;
    }
    if (fileTypes.lockFiles.patterns?.some(pattern => {
      if (pattern.includes('*')) {
        const regexPattern = pattern.replace(/\*/g, '.*');
        const regex = new RegExp(`^${regexPattern}$`, 'i');
        return regex.test(name);
      }
      return lowerName === pattern.toLowerCase();
    })) {
      return true;
    }
  }

  return false;
};

/**
 * Check if a directory matches directory filters
 */
const matchesDirectoryFilters = (name: string, filters: AutoSelectFilters): boolean => {
  const lowerName = name.toLowerCase();
  const { directories } = filters;

  // Node modules - exact name match only
  if (directories.nodeModules.enabled) {
    if (directories.nodeModules.patterns.some(pattern => lowerName === pattern.toLowerCase())) {
      return true;
    }
  }

  // Build outputs - exact name match only
  if (directories.buildOutputs.enabled) {
    if (directories.buildOutputs.patterns.some(pattern => lowerName === pattern.toLowerCase())) {
      return true;
    }
  }

  // Version control - exact name match only
  if (directories.versionControl.enabled) {
    if (directories.versionControl.patterns.some(pattern => lowerName === pattern.toLowerCase())) {
      return true;
    }
  }

  // IDE files - exact name match only
  if (directories.ideFiles.enabled) {
    if (directories.ideFiles.patterns.some(pattern => lowerName === pattern.toLowerCase())) {
      return true;
    }
  }

  return false;
};

/**
 * Main function to determine if a file/directory should be ignored in auto-select
 */
export const shouldIgnoreFile = (
  name: string,
  isLeaf: boolean,
  fullPath: string,
  gitignorePatterns: string[] = [],
  filters?: AutoSelectFilters
): boolean => {
  // Always check gitignore patterns first
  for (const pattern of gitignorePatterns) {
    if (matchesGitignorePattern(name, fullPath, pattern)) {
      return true;
    }
  }

  // If no filters provided, don't filter anything else (let user choose)
  if (!filters) {
    return false;
  }

  const { hiddenFiles } = filters;

  // Check hidden files (both files and directories)
  if (hiddenFiles.enabled && name.startsWith('.')) {
    return true;
  }

  // For files - check file type filters
  if (isLeaf) {
    return matchesFileTypeFilters(name, fullPath, filters);
  } else {
    // For directories - check directory filters with exact matching
    return matchesDirectoryFilters(name, filters);
  }
};

/**
 * Read .gitignore file from project root
 */
export const readGitignoreFile = async (projectPath: string): Promise<string[]> => {
  try {
    const gitignorePath = `${projectPath}/.gitignore`;
    const content = await window.electronAPI.readFile(gitignorePath);
    return parseGitignorePatterns(content);
  } catch (error) {
    // .gitignore file doesn't exist or can't be read
    return [];
  }
};


---

// FILE: src\shared\utils\formatters.ts

/**
 * @file formatters.ts
 * @description Utility functions for formatting numbers and file sizes
 */

/**
 * Format file size in bytes to human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  const value = bytes / Math.pow(k, i);
  
  if (value >= 100) {
    return `${Math.round(value)} ${sizes[i]}`;
  } else if (value >= 10) {
    return `${Math.round(value * 10) / 10} ${sizes[i]}`;
  } else {
    return `${Math.round(value * 100) / 100} ${sizes[i]}`;
  }
}

/**
 * Format large numbers to compact format (1.5K, 103M, etc.)
 */
export function formatCompactNumber(num: number): string {
  if (num === 0) return '0';
  
  const absNum = Math.abs(num);
  const sign = num < 0 ? '-' : '';
  
  if (absNum < 1000) {
    return sign + absNum.toString();
  }
  
  const units = [
    { value: 1e9, suffix: 'B' },
    { value: 1e6, suffix: 'M' },
    { value: 1e3, suffix: 'K' }
  ];
  
  for (const unit of units) {
    if (absNum >= unit.value) {
      const value = absNum / unit.value;
      
      if (value >= 100) {
        return sign + Math.round(value) + unit.suffix;
      } else if (value >= 10) {
        return sign + (Math.round(value * 10) / 10) + unit.suffix;
      } else {
        return sign + (Math.round(value * 100) / 100) + unit.suffix;
      }
    }
  }
  
  return sign + absNum.toString();
}

/**
 * Format character count to compact format
 */
export function formatCharCount(chars: number): string {
  return formatCompactNumber(chars) + ' chars';
}

/**
 * Format token count to compact format
 */
export function formatTokenCount(tokens: number): string {
  return formatCompactNumber(tokens) + ' tokens';
}

/**
 * Estimate token count from character count (rough approximation)
 * Average ratio is about 4 characters per token for English text
 */
export function estimateTokenCount(chars: number): number {
  return Math.round(chars / 4);
}

/**
 * Format file count to compact format
 */
export function formatFileCount(count: number): string {
  if (count === 0) return '0 files';
  if (count === 1) return '1 file';
  
  return formatCompactNumber(count) + ' files';
}


---

// FILE: tsconfig.json

{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": false,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "allowImportingTsExtensions": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "include": ["src", "electron"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
