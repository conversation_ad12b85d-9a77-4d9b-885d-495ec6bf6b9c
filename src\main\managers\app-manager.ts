/**
 * @file src/main/managers/app-manager.ts
 * @description Main application manager that coordinates all components.
 * Handles application lifecycle, menu creation, and component initialization.
 */

import { app, Menu, session } from 'electron';
import { WindowManager } from './window-manager';
import { WorkerManager } from './worker-manager';
import { IPCRouter } from '../ipc/ipc-router';
import { mcpApiService } from '../services/mcp_api';
import { isMac } from '../utils/environment';

export class AppManager {
  private windowManager: WindowManager;
  private workerManager: WorkerManager;
  private ipcRouter: IPCRouter;
  private isDev: boolean;
  private viteDevServerUrl: string | null;

  constructor() {
    // Environment setup
    this.isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;
    this.viteDevServerUrl = process.env['VITE_DEV_SERVER_URL'] || (this.isDev ? 'http://localhost:5173' : null);

    console.log('[AppManager] Environment check:');
    console.log('[AppManager] NODE_ENV:', process.env.NODE_ENV);
    console.log('[AppManager] isPackaged:', app.isPackaged);
    console.log('[AppManager] isDev:', this.isDev);
    console.log('[AppManager] VITE_DEV_SERVER_URL:', this.viteDevServerUrl);

    // Initialize managers
    this.windowManager = new WindowManager(this.isDev, this.viteDevServerUrl);
    this.workerManager = new WorkerManager(this.windowManager);
    this.ipcRouter = new IPCRouter(this.windowManager, this.workerManager);

    this.setupEventHandlers();
  }

  /**
   * Initialize the application
   */
  async initialize(): Promise<void> {
    // Set up Content Security Policy
    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            this.isDev
              ? "default-src 'self' 'unsafe-inline' data:; script-src 'self' 'unsafe-eval' 'unsafe-inline' data:"
              : "default-src 'self'"
          ]
        }
      });
    });

    this.createApplicationMenu();
    await this.windowManager.createWindow();
    this.workerManager.createLangChainWorker();
    await this.ipcRouter.initializeVectorization();
    mcpApiService.start();

    console.log('[AppManager] Application initialized successfully');
  }

  /**
   * Setup application event handlers
   */
  private setupEventHandlers(): void {
    app.on('ready', async () => {
      await this.initialize();
    });

    app.on('window-all-closed', () => {
      mcpApiService.stop();
      this.workerManager.terminateAll();
      if (process.platform !== 'darwin') app.quit();
    });

    app.on('activate', async () => {
      const { BrowserWindow } = require('electron');
      if (BrowserWindow.getAllWindows().length === 0) {
        await this.windowManager.createWindow();
      }
    });
  }

  /**
   * Create application menu
   */
  private createApplicationMenu(): void {
    const template = [
      ...(isMac ? [{
        label: app.getName(),
        submenu: [
          { role: 'about' },
          { type: 'separator' },
          { role: 'services' },
          { type: 'separator' },
          { role: 'hide' },
          { role: 'hideOthers' },
          { role: 'unhide' },
          { type: 'separator' },
          { role: 'quit' }
        ]
      }] : []),
      {
        label: 'File',
        submenu: [
          isMac ? { role: 'close' } : { role: 'quit' }
        ]
      },
      {
        label: 'Edit',
        submenu: [
          { role: 'undo' },
          { role: 'redo' },
          { type: 'separator' },
          { role: 'cut' },
          { role: 'copy' },
          { role: 'paste' }
        ]
      },
      {
        label: 'View',
        submenu: [
          { role: 'reload' },
          { role: 'forceReload' },
          { role: 'toggleDevTools' },
          { type: 'separator' },
          { role: 'resetZoom' },
          { role: 'zoomIn' },
          { role: 'zoomOut' },
          { type: 'separator' },
          { role: 'togglefullscreen' }
        ]
      },
      {
        label: 'Window',
        submenu: [
          { role: 'minimize' },
          { role: 'close' }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template as any);
    Menu.setApplicationMenu(menu);
  }

  /**
   * Get manager instances for advanced usage
   */
  getManagers() {
    return {
      window: this.windowManager,
      worker: this.workerManager,
      ipc: this.ipcRouter
    };
  }
}
