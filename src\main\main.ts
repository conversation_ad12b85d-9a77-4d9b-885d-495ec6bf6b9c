/**
 * @file src/main/main.ts
 * @description Main process entry point for the Electron application.
 * Uses modular architecture with dedicated managers and IPC handlers.
 */

import { app } from 'electron';
import { AppManager } from './managers/app-manager';

// Handle creating/removing shortcuts on Windows when installing/uninstalling.
if (require('electron-squirrel-startup')) {
  app.quit();
}

// Initialize the application manager
const appManager = new AppManager();

// All functionality has been moved to AppManager and its components
console.log('[Main] SmartRAG application starting with modular architecture...');




