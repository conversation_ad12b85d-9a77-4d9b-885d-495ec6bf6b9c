/**
 * @file src/main/managers/window-manager.ts
 * @description Window management for the Electron application.
 * Handles window creation, lifecycle, and dev server loading.
 */

import { BrowserWindow, session } from 'electron';
import path from 'path';

export class WindowManager {
  private mainWindow: BrowserWindow | null = null;
  private isDev: boolean;
  private viteDevServerUrl: string | null;

  constructor(isDev: boolean, viteDevServerUrl: string | null) {
    this.isDev = isDev;
    this.viteDevServerUrl = viteDevServerUrl;
  }

  /**
   * Create the main application window
   */
  async createWindow(): Promise<BrowserWindow> {
    // Set up Content Security Policy for security
    session.defaultSession.webRequest.onHeadersReceived((details, callback) => {
      callback({
        responseHeaders: {
          ...details.responseHeaders,
          'Content-Security-Policy': [
            this.isDev
              ? "default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob: ws: http://localhost:*; script-src 'self' 'unsafe-inline' 'unsafe-eval' http://localhost:*; style-src 'self' 'unsafe-inline' http://localhost:*;"
              : "default-src 'self' data: blob:; script-src 'self'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:;"
          ]
        }
      });
    });

    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      webPreferences: {
        preload: path.join(__dirname, 'preload.js'),
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true, // Explicitly enable web security
      },
    });

    if (this.viteDevServerUrl) {
      console.log('[WindowManager] Attempting to load Vite dev server:', this.viteDevServerUrl);
      await this.loadDevServer();
      this.mainWindow.webContents.openDevTools();
    } else {
      console.log('[WindowManager] Loading production build...');
      this.mainWindow.loadFile(path.join(__dirname, '../index.html'));
      this.mainWindow.webContents.openDevTools();
    }

    return this.mainWindow;
  }

  /**
   * Load development server with retry logic
   */
  private async loadDevServer(retries = 2, delay = 1500): Promise<void> {
    console.log(`[WindowManager] Starting dev server connection attempts to: ${this.viteDevServerUrl}`);
    
    for (let i = 0; i < retries; i++) {
      try {
        console.log(`[WindowManager] Attempting to load Vite dev server (attempt ${i + 1}/${retries})...`);
        
        // Загружаем URL напрямую без fetch проверки
        await this.mainWindow!.loadURL(this.viteDevServerUrl!);
        console.log('[WindowManager] ✅ Successfully loaded Vite dev server');
        return;
      } catch (error) {
        console.log(`[WindowManager] ❌ Failed to load Vite dev server (attempt ${i + 1}/${retries}):`, error);
        if (i < retries - 1) {
          console.log(`[WindowManager] ⏳ Retrying in ${delay}ms...`);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }
    
    console.error('[WindowManager] ❌ Failed to load Vite dev server after all retries.');
    console.log('[WindowManager] 🔄 Attempting to load fallback (production build)...');
    
    // Fallback to production build if available
    try {
      const fallbackPath = path.join(__dirname, '../index.html');
      console.log('[WindowManager] Fallback path:', fallbackPath);
      await this.mainWindow!.loadFile(fallbackPath);
      console.log('[WindowManager] ✅ Fallback loaded successfully');
    } catch (fallbackError) {
      console.error('[WindowManager] ❌ Failed to load fallback file:', fallbackError);
      // Последний fallback - простой HTML
      this.mainWindow!.loadURL('data:text/html;charset=utf-8,<html><body style="background:#1e1e1e;color:white;padding:20px;font-family:Arial;"><h1>❌ Ошибка загрузки</h1><p>Не удалось загрузить ни dev сервер, ни production файлы.</p><p>Проверьте консоль для деталей.</p></body></html>');
    }
  }

  /**
   * Get the main window instance
   */
  getMainWindow(): BrowserWindow | null {
    return this.mainWindow;
  }

  /**
   * Send message to renderer process
   */
  sendToRenderer(channel: string, data: any): void {
    this.mainWindow?.webContents.send(channel, data);
  }
}
