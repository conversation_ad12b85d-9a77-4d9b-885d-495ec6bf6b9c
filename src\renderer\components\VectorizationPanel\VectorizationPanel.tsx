/**
 * @file src/renderer/components/VectorizationPanel/VectorizationPanel.tsx
 * @description Main vectorization panel component for the Dashboard tab
 */

import React, { useState, useEffect, useCallback } from 'react';
import { Box, Typography, Alert, Divider } from '@mui/material';
import { VectorizationControls } from './VectorizationControls';
import { VectorizationProgress } from './VectorizationProgress';
import { VectorizationResults } from './VectorizationResults';
import { VectorizationErrorBoundary } from './VectorizationErrorBoundary';
import type { Project } from '../../../shared/ipc';
import type {
  VectorizationConfig,
  VectorizationStats,
  FileVectorizationInfo
} from '../../../shared/types/vectorization.types';
import { DEFAULT_VECTORIZATION_CONFIG } from '../../../shared/types/vectorization.types';

interface VectorizationProgressItem {
  id: string;
  file: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  chunks: number;
  progress: number;
  error?: string;
  startTime?: number;
  endTime?: number;
}

interface VectorizationPanelProps {
  project: Project | null;
}

const VectorizationPanelContent: React.FC<VectorizationPanelProps> = ({ project }) => {
  // Configuration state
  const [config, setConfig] = useState<VectorizationConfig>(DEFAULT_VECTORIZATION_CONFIG);

  // Progress state
  const [isVectorizing, setIsVectorizing] = useState(false);
  const [currentFile, setCurrentFile] = useState<string>();
  const [overallProgress, setOverallProgress] = useState(0);
  const [completedFiles, setCompletedFiles] = useState(0);
  const [totalFiles, setTotalFiles] = useState(0);
  const [progressItems, setProgressItems] = useState<VectorizationProgressItem[]>([]);
  const [startTime, setStartTime] = useState<number>();

  // Results state
  const [stats, setStats] = useState<VectorizationStats | null>(null);
  const [fileInfo, setFileInfo] = useState<FileVectorizationInfo[]>([]);
  const [availableChunks, setAvailableChunks] = useState(0);
  const [vectorizedChunks, setVectorizedChunks] = useState(0);

  // Load configuration from localStorage
  useEffect(() => {
    if (project?.id) {
      const savedConfig = localStorage.getItem(`vectorization_config_${project.id}`);
      if (savedConfig) {
        try {
          const parsedConfig = JSON.parse(savedConfig);
          setConfig((prev: VectorizationConfig) => ({ ...prev, ...parsedConfig }));
        } catch (error) {
          console.error('Error loading vectorization config:', error);
        }
      }
    }
  }, [project?.id]);

  // Save configuration to localStorage
  const saveConfig = useCallback((newConfig: VectorizationConfig) => {
    if (project?.id) {
      localStorage.setItem(`vectorization_config_${project.id}`, JSON.stringify(newConfig));
    }
  }, [project?.id]);

  // Load vectorization stats and chunk information
  const loadVectorizationData = useCallback(async () => {
    if (!project?.id) return;

    try {
      const stats = await window.electronAPI.getVectorizationStats(project.id);
      const fileInfo = await window.electronAPI.getFileVectorizationInfo(project.id);

      setStats(stats);
      setAvailableChunks(stats.totalChunks);
      setVectorizedChunks(stats.vectorizedChunks);
      setFileInfo(fileInfo);
    } catch (error) {
      console.error('Error loading vectorization data:', error);
      // Set empty state on error
      setStats(null);
      setFileInfo([]);
      setAvailableChunks(0);
      setVectorizedChunks(0);
    }
  }, [project?.id]);

  // Load data when project changes
  useEffect(() => {
    loadVectorizationData();
  }, [loadVectorizationData]);

  // Subscribe to vectorization updates
  useEffect(() => {
    const unsubscribe = window.electronAPI.onVectorizationUpdate((payload) => {
      console.log('Vectorization update received:', payload);

      // Only handle updates for current project
      if (payload.projectId !== project?.id) return;

      switch (payload.type) {
        case 'progress':
          if (payload.progress !== undefined) setOverallProgress(payload.progress);
          if (payload.file) setCurrentFile(payload.file);
          if (payload.completedFiles !== undefined) setCompletedFiles(payload.completedFiles);
          if (payload.totalFiles !== undefined) setTotalFiles(payload.totalFiles);
          break;

        case 'file_complete':
          if (payload.completedFiles !== undefined) setCompletedFiles(payload.completedFiles);
          break;

        case 'batch_complete':
          setIsVectorizing(false);
          setOverallProgress(100);
          if (payload.stats) {
            setStats(payload.stats);
            setVectorizedChunks(payload.stats.vectorizedChunks);
          }
          loadVectorizationData(); // Refresh all data
          break;

        case 'error':
          setIsVectorizing(false);
          console.error('Vectorization error:', payload.error);
          break;
      }
    });

    return unsubscribe;
  }, [project?.id, loadVectorizationData]);

  // Handle configuration change
  const handleConfigChange = useCallback((newConfig: VectorizationConfig) => {
    setConfig(newConfig);
    saveConfig(newConfig);
  }, [saveConfig]);

  // Handle start vectorization
  const handleStartVectorization = useCallback(async () => {
    if (!project?.id || isVectorizing) return;

    try {
      setIsVectorizing(true);
      setStartTime(Date.now());
      setOverallProgress(0);
      setCompletedFiles(0);
      setProgressItems([]);

      const result = await window.electronAPI.startVectorization({
        projectId: project.id,
        config: config
      });

      if (!result.success) {
        throw new Error(result.message || 'Failed to start vectorization');
      }

      console.log('Vectorization started successfully');

    } catch (error) {
      console.error('Error starting vectorization:', error);
      setIsVectorizing(false);
    }
  }, [project?.id, isVectorizing, config]);

  // Handle stop vectorization
  const handleStopVectorization = useCallback(async () => {
    if (!project?.id) return;

    try {
      const result = await window.electronAPI.stopVectorization(project.id);

      if (result.success) {
        setIsVectorizing(false);
        console.log('Vectorization stopped successfully');
      } else {
        console.error('Failed to stop vectorization:', result.message);
      }
    } catch (error) {
      console.error('Error stopping vectorization:', error);
    }
  }, [project?.id]);

  // Handle test search
  const handleTestSearch = useCallback(async () => {
    if (!project?.id) return;

    try {
      const results = await window.electronAPI.testVectorSearch({
        projectId: project.id,
        query: 'test search query',
        k: 5
      });

      console.log('Vector search results:', results);
      // TODO: Show results in a dialog or panel
    } catch (error) {
      console.error('Error testing vector search:', error);
    }
  }, [project?.id]);

  // Handle clear vectors
  const handleClearVectors = useCallback(async () => {
    if (!project?.id) return;

    try {
      const result = await window.electronAPI.clearVectors(project.id);

      if (result.success) {
        setStats(null);
        setFileInfo([]);
        setVectorizedChunks(0);
        console.log('Vectors cleared successfully');
      } else {
        console.error('Failed to clear vectors:', result.message);
      }
    } catch (error) {
      console.error('Error clearing vectors:', error);
    }
  }, [project?.id]);

  if (!project) {
    return (
      <Box sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100%',
        p: 4,
        textAlign: 'center'
      }}>
        <Typography variant="h6" sx={{ color: '#9ca3af', mb: 2 }}>
          No Project Selected
        </Typography>
        <Typography variant="body2" sx={{ color: '#64748b' }}>
          Please select a project to manage vectorization.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{
      p: 3,
      height: '100%',
      overflow: 'auto',
      background: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%)',
    }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="h4" sx={{ color: '#d8b4fe', fontWeight: 700, mb: 1 }}>
          Vectorization
        </Typography>
        <Typography variant="body1" sx={{ color: '#94a3b8' }}>
          Convert your chunked documents into searchable vector embeddings
        </Typography>
      </Box>

      {/* Project Info */}
      <Alert 
        severity="info" 
        sx={{ 
          mb: 3,
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          border: '1px solid rgba(59, 130, 246, 0.2)',
          color: '#60a5fa'
        }}
      >
        Working with project: <strong>{project.name}</strong>
      </Alert>

      {/* Controls */}
      <VectorizationControls
        config={config}
        onConfigChange={handleConfigChange}
        onStartVectorization={handleStartVectorization}
        onStopVectorization={handleStopVectorization}
        isVectorizing={isVectorizing}
        availableChunks={availableChunks}
        vectorizedChunks={vectorizedChunks}
      />

      <Divider sx={{ my: 3, borderColor: 'rgba(147, 51, 234, 0.1)' }} />

      {/* Progress */}
      {(isVectorizing || progressItems.length > 0) && (
        <>
          <VectorizationProgress
            isActive={isVectorizing}
            currentFile={currentFile}
            overallProgress={overallProgress}
            completedFiles={completedFiles}
            totalFiles={totalFiles}
            items={progressItems}
            startTime={startTime}
          />
          <Divider sx={{ my: 3, borderColor: 'rgba(147, 51, 234, 0.1)' }} />
        </>
      )}

      {/* Results */}
      <VectorizationResults
        stats={stats}
        fileInfo={fileInfo}
        onTestSearch={handleTestSearch}
        onClearVectors={handleClearVectors}
        onRefreshStats={loadVectorizationData}
      />
    </Box>
  );
};

// Export wrapped component with Error Boundary
export const VectorizationPanel: React.FC<VectorizationPanelProps> = (props) => (
  <VectorizationErrorBoundary>
    <VectorizationPanelContent {...props} />
  </VectorizationErrorBoundary>
);
