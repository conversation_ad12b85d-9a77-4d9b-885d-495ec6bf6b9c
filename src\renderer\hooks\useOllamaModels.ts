/**
 * @file src/renderer/hooks/useOllamaModels.ts
 * @description React hook for fetching and managing Ollama models.
 */

import { useState, useEffect, useCallback } from 'react';

export interface OllamaModel {
  name: string;
  model: string;
  modified_at: string;
  size: number;
  digest: string;
  details?: {
    parent_model?: string;
    format?: string;
    family?: string;
    families?: string[];
    parameter_size?: string;
    quantization_level?: string;
  };
}

export interface OllamaModelsState {
  allModels: OllamaModel[];
  embeddingModels: string[];
  llmModels: string[];
  isLoading: boolean;
  error: string | null;
  isServerRunning: boolean;
}

export interface UseOllamaModelsReturn extends OllamaModelsState {
  refreshModels: () => Promise<void>;
  checkServer: () => Promise<void>;
}

export const useOllamaModels = (): UseOllamaModelsReturn => {
  const [state, setState] = useState<OllamaModelsState>({
    allModels: [],
    embeddingModels: [],
    llmModels: [],
    isLoading: false,
    error: null,
    isServerRunning: false,
  });

  const checkServer = useCallback(async () => {
    try {
      const result = await window.electronAPI.invoke('ollama:checkServer');
      setState(prev => ({
        ...prev,
        isServerRunning: result.isRunning,
        error: result.success ? null : result.error
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        isServerRunning: false,
        error: error instanceof Error ? error.message : 'Failed to check server'
      }));
    }
  }, []);

  const refreshModels = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Check server first
      await checkServer();

      // Fetch all models in parallel
      const [allModelsResult, embeddingModelsResult, llmModelsResult] = await Promise.all([
        window.electronAPI.invoke('ollama:getModels'),
        window.electronAPI.invoke('ollama:getEmbeddingModels'),
        window.electronAPI.invoke('ollama:getLLMModels'),
      ]);

      setState(prev => ({
        ...prev,
        allModels: allModelsResult.models || [],
        embeddingModels: embeddingModelsResult.models || [],
        llmModels: llmModelsResult.models || [],
        isLoading: false,
        error: null
      }));

    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to fetch models'
      }));
    }
  }, [checkServer]);

  // Initial load
  useEffect(() => {
    refreshModels();
  }, [refreshModels]);

  return {
    ...state,
    refreshModels,
    checkServer,
  };
};
