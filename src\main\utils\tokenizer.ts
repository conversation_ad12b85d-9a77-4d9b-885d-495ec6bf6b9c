/**
 * Utility for counting tokens in text
 * For MVP we use a simple estimation, can be replaced with Ollama API for accuracy
 */

// Lang<PERSON>hain splitter removed - using simple estimation

export function countTokens(text: string): number {
  // Simple token estimation: ~4 characters per token
  return Math.ceil(text.length / 4);
}

export function estimateTokensFromLines(lines: number): number {
  // Rough estimation: average 15 tokens per line of code
  return lines * 15;
}

export function splitByTokenLimit(text: string, maxTokens: number, overlap: number = 0): string[] {
  const lines = text.split('\n');
  const chunks: string[] = [];
  let currentChunk: string[] = [];
  let currentTokens = 0;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const lineTokens = countTokens(line);
    
    // If adding this line would exceed the limit, save current chunk
    if (currentTokens + lineTokens > maxTokens && currentChunk.length > 0) {
      chunks.push(currentChunk.join('\n'));
      
      // Start new chunk with overlap
      if (overlap > 0 && currentChunk.length > overlap) {
        currentChunk = currentChunk.slice(-overlap);
        currentTokens = countTokens(currentChunk.join('\n'));
      } else {
        currentChunk = [];
        currentTokens = 0;
      }
    }
    
    currentChunk.push(line);
    currentTokens += lineTokens;
  }
  
  // Add the last chunk if it has content
  if (currentChunk.length > 0) {
    chunks.push(currentChunk.join('\n'));
  }
  
  return chunks;
}
