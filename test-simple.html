<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Test</title>
    <style>
        body {
            background-color: #1e1e1e;
            color: white;
            font-family: Arial, sans-serif;
            padding: 20px;
        }
        .test-box {
            background-color: #2d2d2d;
            border: 2px solid #4CAF50;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🔧 Диагностический тест Electron</h1>
    
    <div class="test-box">
        <h2>✅ HTML загружается</h2>
        <p>Если вы видите этот текст, то HTML файл загружается корректно.</p>
    </div>

    <div class="test-box">
        <h2>🔍 Проверка JavaScript</h2>
        <p id="js-test">❌ JavaScript не работает</p>
    </div>

    <div class="test-box">
        <h2>🔗 Проверка ElectronAPI</h2>
        <p id="api-test">Проверяем...</p>
    </div>

    <div class="test-box">
        <h2>📝 Консольные логи</h2>
        <p>Откройте DevTools (F12) и посмотрите консоль для дополнительной информации.</p>
    </div>

    <script>
        console.log('=== ДИАГНОСТИЧЕСКИЙ ТЕСТ НАЧАТ ===');
        
        // Тест 1: JavaScript работает
        document.getElementById('js-test').innerHTML = '✅ JavaScript работает!';
        document.getElementById('js-test').style.color = '#4CAF50';
        console.log('✅ JavaScript работает');

        // Тест 2: Проверка window объекта
        console.log('Window object:', window);
        console.log('Document:', document);
        console.log('Location:', window.location);

        // Тест 3: Проверка ElectronAPI
        if (window.electronAPI) {
            document.getElementById('api-test').innerHTML = '✅ ElectronAPI доступен';
            document.getElementById('api-test').style.color = '#4CAF50';
            console.log('✅ ElectronAPI доступен:', window.electronAPI);
        } else {
            document.getElementById('api-test').innerHTML = '❌ ElectronAPI недоступен';
            document.getElementById('api-test').style.color = '#f44336';
            console.log('❌ ElectronAPI недоступен');
        }

        // Тест 4: Проверка процесса
        if (window.process) {
            console.log('✅ Process доступен:', window.process);
        } else {
            console.log('❌ Process недоступен');
        }

        // Тест 5: Проверка require (не должен быть доступен в renderer)
        if (typeof require !== 'undefined') {
            console.log('⚠️ require доступен (небезопасно)');
        } else {
            console.log('✅ require недоступен (безопасно)');
        }

        console.log('=== ДИАГНОСТИЧЕСКИЙ ТЕСТ ЗАВЕРШЕН ===');
    </script>
</body>
</html>
