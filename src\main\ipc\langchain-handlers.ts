/**
 * @file src/main/ipc/langchain-handlers.ts
 * @description IPC handlers for LangChain RAG operations - REMOVED.
 * LangChain functionality has been removed from the project.
 */

import { ipcMain } from 'electron';
import { WorkerManager } from '../managers/worker-manager';

export class LangChainHandlers {
  private workerManager: WorkerManager;

  constructor(workerManager: WorkerManager) {
    this.workerManager = workerManager;
    this.registerHandlers();
  }

  private registerHandlers(): void {
    // LangChain functionality has been removed
    ipcMain.handle('langchain:indexSelectedFiles', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:indexFile', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:reindexFile', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:query', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:getProjectStats', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:updateConfig', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:cancelIndexing', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    ipcMain.handle('langchain:clearProjectChunks', async () => {
      return {
        success: false,
        error: 'LangChain functionality has been removed from this version'
      };
    });

    console.log('[LangChainHandlers] LangChain IPC handlers registered (functionality removed)');
  }
}
