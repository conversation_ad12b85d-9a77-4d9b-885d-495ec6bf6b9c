/**
 * @file src/main/ipc/langchain-handlers.ts
 * @description IPC handlers for LangChain RAG operations.
 * Handles indexing, querying, and configuration management.
 */

import { ipcMain } from 'electron';
import { WorkerManager } from '../managers/worker-manager';

export class LangChainHandlers {
  private workerManager: WorkerManager;

  constructor(workerManager: WorkerManager) {
    this.workerManager = workerManager;
    this.registerHandlers();
  }

  private registerHandlers(): void {
    // LangChain RAG - индексация выбранных файлов
    ipcMain.handle('langchain:indexSelectedFiles', async (_, { filePaths, projectId, config }: {
      filePaths: string[];
      projectId: number;
      config?: any;
    }) => {
      console.log('[LangChainHandlers] Posting batch indexing job to LangChain worker for project', projectId);

      try {
        this.workerManager.postToLangChainWorker({
          type: 'index_batch',
          filePaths,
          projectId,
          config
        });

        return {
          success: true,
          message: `Started batch indexing ${filePaths.length} files with <PERSON><PERSON><PERSON><PERSON>`
        };

      } catch (error) {
        console.error('[LangChainHandlers] Error in indexSelectedFiles:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });

    // LangChain RAG - индексация одного файла
    ipcMain.handle('langchain:indexFile', async (_, { filePath, projectId, config }: {
      filePath: string;
      projectId: number;
      config?: any;
    }) => {
      console.log('[LangChainHandlers] Starting LangChain indexing of file:', filePath, 'for project', projectId);

      try {
        this.workerManager.postToLangChainWorker({
          type: 'index_file',
          filePath,
          projectId,
          config
        });

        return {
          success: true,
          message: `Started indexing ${filePath} with LangChain`
        };

      } catch (error) {
        console.error('[LangChainHandlers] Error in indexFile:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });

    // LangChain RAG - переиндексация файла
    ipcMain.handle('langchain:reindexFile', async (_, { filePath, projectId, config }: {
      filePath: string;
      projectId: number;
      config?: any;
    }) => {
      console.log('[LangChainHandlers] Starting LangChain reindexing of file:', filePath, 'for project', projectId);

      try {
        this.workerManager.postToLangChainWorker({
          type: 'reindex_file',
          filePath,
          projectId,
          config
        });

        return {
          success: true,
          message: `Started reindexing ${filePath} with LangChain`
        };

      } catch (error) {
        console.error('[LangChainHandlers] Error in reindexFile:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });

    // LangChain RAG - выполнение запроса
    ipcMain.handle('langchain:query', async (_, { projectId, question, options }: {
      projectId: number;
      question: string;
      options?: any;
    }) => {
      console.log('[LangChainHandlers] LangChain RAG query for project', projectId, ':', question);

      try {
        // Import RAG service directly for queries (not in worker to avoid serialization issues)
        const { ragService } = await import('../services/langchain-rag');

        const response = await ragService.query(projectId, question, options);

        return {
          success: true,
          response
        };

      } catch (error) {
        console.error('[LangChainHandlers] Error in query:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });

    // LangChain RAG - получение статистики проекта
    ipcMain.handle('langchain:getProjectStats', async (_, { projectId }: { projectId: number }) => {
      console.log('[LangChainHandlers] Getting LangChain project stats for project', projectId);

      try {
        const { ragService } = await import('../services/langchain-rag');

        const stats = await ragService.getProjectStats(projectId);

        return {
          success: true,
          stats
        };

      } catch (error) {
        console.error('[LangChainHandlers] Error getting project stats:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });

    // LangChain RAG - обновление конфигурации
    ipcMain.handle('langchain:updateConfig', async (_, { config }: { config: any }) => {
      console.log('[LangChainHandlers] Updating LangChain config:', config);

      try {
        this.workerManager.postToLangChainWorker({
          type: 'update_config',
          config
        });

        return {
          success: true,
          message: 'LangChain config updated'
        };

      } catch (error) {
        console.error('[LangChainHandlers] Error updating config:', error);
        return {
          success: false,
          error: error instanceof Error ? error.message : String(error)
        };
      }
    });

    // LangChain RAG - отмена индексации
    ipcMain.handle('langchain:cancelIndexing', async () => {
      console.log('[LangChainHandlers] Cancelling LangChain indexing...');
      this.workerManager.postToLangChainWorker({ type: 'cancel_indexing' });
      return { success: true, message: 'Indexing cancellation requested.' };
    });

    // LangChain RAG - очистка chunks проекта
    ipcMain.handle('langchain:clearProjectChunks', async (_, { projectId }: { projectId: number }) => {
      console.log('[LangChainHandlers] Clearing chunks for project', projectId);

      try {
        const { ragService } = await import('../services/langchain-rag');
        await ragService.clearProjectChunks(projectId);

        return {
          success: true,
          message: `Chunks cleared for project ${projectId}`
        };
      } catch (error) {
        console.error('[LangChainHandlers] Error clearing project chunks:', error);
        return {
          success: false,
          message: `Error clearing chunks: ${(error as Error).message}`
        };
      }
    });

    console.log('[LangChainHandlers] All LangChain IPC handlers registered');
  }
}
