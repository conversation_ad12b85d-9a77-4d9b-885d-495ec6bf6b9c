/**
 * @file src/shared/ipc.d.ts
 * @description Type definitions for IPC communication between main and renderer processes.
 */

// Import shared vectorization types
import type {
  VectorizationConfig,
  VectorizationStats,
  FileVectorizationInfo,
  VectorizationUpdatePayload,
  VectorSearchRequest,
  VectorSearchResult
} from './types/vectorization.types';

export interface Project {
  id: number;
  name: string;
  path: string;
  dataPath: string;
  createdAt: string;
}

export interface FileTreeNode {
  id: string;
  name: string;
  children?: FileTreeNode[];
  hasChildren?: boolean; // Hint for lazy loading
  size?: number; // File/directory size in bytes
  tokenCount?: number; // Token count for files
  isDirectory?: boolean; // Whether this is a directory
}

export interface IndexingUpdatePayload {
  status: 'completed' | 'error' | 'progress' | 'batch_progress' | 'batch_completed' | 'cancelled';
  file?: string;
  error?: string;
  progress?: number;
  result?: any;
  batchProgress?: {
    completed: number;
    total: number;
    currentFile: string;
    elapsedTime: number;
    estimatedRemainingTime: number;
    startTime: number;
  };
}

export interface ProjectFile {
  id: string;
  originalPath: string;
  storedPath: string;
  fileName: string;
  fileHash: string;
  size: number;
  addedAt: string;
  projectId: number;
}



export interface IpcApi {
  // Indexing
  startIndexing: (payload: { filePath: string; projectId: number }) => void;
  onIndexingUpdate: (callback: (payload: IndexingUpdatePayload) => void) => () => void;

  // RAG - индексация выбранных файлов
  indexSelectedFiles: (payload: { filePaths: string[]; projectId: number }) => Promise<{
    success: boolean;
    chunkingStats?: Array<{
      file: string;
      totalChunks: number;
      totalTokens: number;
      avgTokens: number;
      maxTokens: number;
      semanticUnits: {
        functions: number;
        classes: number;
        interfaces: number;
      };
    }>;
  }>;

  // LangChain indexing updates
  onLangChainIndexingUpdate: (callback: (payload: any) => void) => () => void;
  cancelIndexing: () => Promise<{ success: boolean; message: string }>;
  clearProjectChunks: (projectId: number) => Promise<{ success: boolean; message: string }>;

  // Vectorization
  startVectorization: (payload: { projectId: number; config: VectorizationConfig }) => Promise<{ success: boolean; message?: string }>;
  stopVectorization: (projectId: number) => Promise<{ success: boolean; message?: string }>;
  getVectorizationStats: (projectId: number) => Promise<VectorizationStats>;
  getFileVectorizationInfo: (projectId: number) => Promise<FileVectorizationInfo[]>;
  clearVectors: (projectId: number) => Promise<{ success: boolean; message?: string }>;
  testVectorSearch: (payload: VectorSearchRequest) => Promise<VectorSearchResult[]>;
  onVectorizationUpdate: (callback: (payload: VectorizationUpdatePayload) => void) => () => void;

  // Projects
  getAllProjects: () => Promise<Project[]>;
  addProject: (path: string) => Promise<Project | null>;
  deleteProject: (id: number) => Promise<void>;

  // Project Files
  addSelectedFiles: (payload: { filePaths: string[]; projectId: number; copyFiles?: boolean }) => Promise<ProjectFile[]>;
  getSelectedFiles: (projectId: number) => Promise<ProjectFile[]>;
  removeSelectedFile: (projectId: number, fileId: string) => Promise<void>;
  clearSelectedFiles: (projectId: number) => Promise<void>;

  // FS
  openDirectoryDialog: () => Promise<string | null>;
  readDirectory: (path: string) => Promise<FileTreeNode[] | null>;
  readDirectoryLazy: (path: string) => Promise<FileTreeNode[] | null>;
  readFile: (filePath: string) => Promise<string>;
  getTokenCount: (filePath: string) => Promise<number>;

  // App Data
  clearAppData: () => Promise<void>;

  // Ollama API
  invoke: (channel: string, ...args: any[]) => Promise<any>;
}

declare global {
  interface Window {
    electronAPI: IpcApi;
  }
}
