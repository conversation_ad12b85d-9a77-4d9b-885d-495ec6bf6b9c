# Metacharts Project Map (High-Level Index)
# Auto-generated: 2025-07-30T22:21:11.083Z
# Purpose: Provides a high-level overview for AI navigation and developer onboarding.
# Stats: 45 files, ~9k lines, ~281k chars, ~~70k tokens

> Legend: An ellipsis (…) at the end of a description means it was truncated. Read the file for full details.

## `./`
- `package.json`: No description found.
- `tsconfig.json`: No description found.

## `src/main/`
- `main.ts`: src/main/main.ts

## `src/main/ipc/`
- `filesystem-handlers.ts`: src/main/ipc/filesystem-handlers.ts
- `ipc-router.ts`: src/main/ipc/ipc-router.ts
- `langchain-handlers.ts`: src/main/ipc/langchain-handlers.ts
- `project-handlers.ts`: src/main/ipc/project-handlers.ts

## `src/main/managers/`
- `app-manager.ts`: src/main/managers/app-manager.ts
- `window-manager.ts`: src/main/managers/window-manager.ts
- `worker-manager.ts`: src/main/managers/worker-manager.ts

## `src/main/services/`
- `langchain-llm.ts`: No description found.
- `mcp_api.ts`: src/main/services/mcp_api.ts
- `ollama-api.ts`: src/main/services/ollama-api.ts
- `project-files.ts`: src/main/services/project-files.ts
- `simple-chunker.ts`: Smart Semantic Chunker with Modern Token-Oriented Approach
- `sqlite.ts`: src/main/services/sqlite.ts

## `src/main/utils/`
- `environment.ts`: No description found.
- `tokenizer.ts`: No description found.

## `src/renderer/`
- `App.tsx`: src/renderer/App.tsx
- `main.tsx`: src/renderer/main.tsx
- `preload.ts`: src/renderer/preload.ts

## `src/renderer/components/`
- `ChatPanel.tsx`: src/renderer/components/ChatPanel.tsx
- `DashboardPanel.tsx`: src/renderer/components/DashboardPanel.tsx
- `EmptyTabContent.tsx`: src/renderer/components/EmptyTabContent.tsx
- `ProjectsPanel.tsx`: src/renderer/components/ProjectsPanel.tsx
- `ProjectTabBar.tsx`: src/renderer/components/ProjectTabBar.tsx
- `QueryInterface.tsx`: src/renderer/components/QueryInterface.tsx
- `SettingsPanel.tsx`: src/renderer/components/SettingsPanel.tsx
- `Sidebar.tsx`: src/renderer/components/Sidebar.tsx

## `src/renderer/components/ExplorerPanel/`
- `ChunkingPanel.tsx`: No description found.
- `FileExplorerPanel.tsx`: src/renderer/components/FileExplorerPanel.tsx
- `FileTree.tsx`: No description found.
- `FilterSettingsDialog.tsx`: src/renderer/components/ExplorerPanel/FilterSettingsDialog.tsx
- `ProjectExplorer.tsx`: src/renderer/components/ProjectExplorer.tsx

## `src/renderer/components/VectorizationPanel/`
- `VectorizationControls.tsx`: src/renderer/components/VectorizationPanel/VectorizationControls.tsx
- `VectorizationErrorBoundary.tsx`: src/renderer/components/VectorizationPanel/VectorizationErrorBoundary.tsx
- `VectorizationPanel.tsx`: src/renderer/components/VectorizationPanel/VectorizationPanel.tsx
- `VectorizationProgress.tsx`: src/renderer/components/VectorizationPanel/VectorizationProgress.tsx
- `VectorizationResults.tsx`: src/renderer/components/VectorizationPanel/VectorizationResults.tsx

## `src/renderer/hooks/`
- `useOllamaModels.ts`: src/renderer/hooks/useOllamaModels.ts

## `src/shared/`
- `store.ts`: store.ts

## `src/shared/hooks/`
- `useGitignore.ts`: src/shared/hooks/useGitignore.ts

## `src/shared/types/`
- `filters.types.ts`: src/shared/types/filters.types.ts

## `src/shared/utils/`
- `fileFilters.ts`: src/shared/utils/fileFilters.ts
- `formatters.ts`: formatters.ts