/**
 * @file src/renderer/components/VectorizationPanel/VectorizationErrorBoundary.tsx
 * @description Error boundary component for vectorization panel to handle and display errors gracefully
 */

import React, { Component, ReactNode } from 'react';
import { Box, Typography, Button, Alert, Paper } from '@mui/material';
import { ErrorOutline, Refresh } from '@mui/icons-material';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

export class VectorizationErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    // Log the error for debugging
    console.error('[VectorizationErrorBoundary] Error caught:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <Paper 
          elevation={2} 
          sx={{ 
            p: 4, 
            m: 2, 
            backgroundColor: '#1e293b',
            border: '1px solid #ef4444'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <ErrorOutline sx={{ color: '#ef4444', mr: 2, fontSize: 32 }} />
            <Typography variant="h5" sx={{ color: '#ef4444', fontWeight: 600 }}>
              Vectorization Error
            </Typography>
          </Box>

          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="body1" sx={{ fontWeight: 600, mb: 1 }}>
              Something went wrong with the vectorization interface.
            </Typography>
            <Typography variant="body2" sx={{ color: '#94a3b8' }}>
              {this.state.error?.message || 'An unexpected error occurred'}
            </Typography>
          </Alert>

          {/* Error Details (Development Mode) */}
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <Box sx={{ mb: 3 }}>
              <Typography variant="h6" sx={{ color: '#f1f5f9', mb: 2 }}>
                Error Details:
              </Typography>
              <Paper 
                sx={{ 
                  p: 2, 
                  backgroundColor: '#0f172a', 
                  border: '1px solid #374151',
                  maxHeight: 200,
                  overflow: 'auto'
                }}
              >
                <Typography 
                  variant="body2" 
                  component="pre" 
                  sx={{ 
                    color: '#ef4444', 
                    fontFamily: 'monospace',
                    fontSize: '0.75rem',
                    whiteSpace: 'pre-wrap'
                  }}
                >
                  {this.state.error.stack}
                </Typography>
              </Paper>
            </Box>
          )}

          {/* Recovery Actions */}
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="contained"
              startIcon={<Refresh />}
              onClick={this.handleReset}
              sx={{
                backgroundColor: '#a855f7',
                '&:hover': { backgroundColor: '#9333ea' }
              }}
            >
              Try Again
            </Button>
            <Button
              variant="outlined"
              onClick={() => window.location.reload()}
              sx={{
                borderColor: '#64748b',
                color: '#e2e8f0',
                '&:hover': { borderColor: '#94a3b8', backgroundColor: 'rgba(148, 163, 184, 0.1)' }
              }}
            >
              Reload Application
            </Button>
          </Box>

          {/* Help Text */}
          <Typography variant="body2" sx={{ color: '#94a3b8', mt: 3 }}>
            If this error persists, try:
          </Typography>
          <Box component="ul" sx={{ color: '#94a3b8', mt: 1, pl: 2 }}>
            <li>Refreshing the application</li>
            <li>Checking if the project has valid chunks</li>
            <li>Verifying vectorization service configuration</li>
            <li>Restarting the application</li>
          </Box>
        </Paper>
      );
    }

    return this.props.children;
  }
}

export default VectorizationErrorBoundary;
