/**
 * @file src/renderer/components/VectorizationPanel/VectorizationResults.tsx
 * @description Results and statistics display for vectorization process
 */

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  Button,
  Collapse,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  ExpandMore,
  ExpandLess,
  Search,
  Delete,
  Refresh,
  CheckCircle,
  Info
} from '@mui/icons-material';
import type {
  VectorizationStats,
  FileVectorizationInfo
} from '../../../shared/types/vectorization.types';

interface VectorizationResultsProps {
  stats: VectorizationStats | null;
  fileInfo: FileVectorizationInfo[];
  onTestSearch?: () => void;
  onClearVectors?: () => void;
  onRefreshStats?: () => void;
  showDetails?: boolean;
}

export const VectorizationResults: React.FC<VectorizationResultsProps> = ({
  stats,
  fileInfo,
  onTestSearch,
  onClearVectors,
  onRefreshStats,
  showDetails = false
}) => {
  const [showFileDetails, setShowFileDetails] = useState(showDetails);

  if (!stats) {
    return (
      <Paper
        sx={{
          p: 3,
          backgroundColor: 'rgba(64, 64, 64, 0.05)',
          border: '1px solid rgba(64, 64, 64, 0.2)',
          borderRadius: 2,
          textAlign: 'center'
        }}
      >
        <Typography variant="body1" sx={{ color: '#9ca3af' }}>
          No vectorization results available yet.
        </Typography>
        <Typography variant="body2" sx={{ color: '#64748b', mt: 1 }}>
          Start vectorization to see statistics and results here.
        </Typography>
      </Paper>
    );
  }

  const completionPercentage = stats.totalChunks > 0 
    ? Math.round((stats.vectorizedChunks / stats.totalChunks) * 100) 
    : 0;

  const getStatusColor = (status: FileVectorizationInfo['status']) => {
    switch (status) {
      case 'complete': return '#22c55e';
      case 'partial': return '#f59e0b';
      case 'pending': return '#64748b';
      case 'error': return '#ef4444';
      default: return '#64748b';
    }
  };

  const getStatusLabel = (status: FileVectorizationInfo['status']) => {
    switch (status) {
      case 'complete': return 'COMPLETE';
      case 'partial': return 'PARTIAL';
      case 'pending': return 'PENDING';
      case 'error': return 'ERROR';
      default: return 'UNKNOWN';
    }
  };

  return (
    <Paper
      sx={{
        p: 3,
        backgroundColor: 'rgba(34, 197, 94, 0.05)',
        border: '1px solid rgba(34, 197, 94, 0.2)',
        borderRadius: 2
      }}
    >
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <CheckCircle sx={{ color: '#22c55e', mr: 1 }} />
          <Typography variant="h6" sx={{ color: '#22c55e', fontWeight: 600 }}>
            Vectorization Results
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          {onTestSearch && (
            <Tooltip title="Test vector search">
              <IconButton
                onClick={onTestSearch}
                size="small"
                sx={{ color: '#60a5fa' }}
              >
                <Search />
              </IconButton>
            </Tooltip>
          )}
          
          {onRefreshStats && (
            <Tooltip title="Refresh statistics">
              <IconButton
                onClick={onRefreshStats}
                size="small"
                sx={{ color: '#9ca3af' }}
              >
                <Refresh />
              </IconButton>
            </Tooltip>
          )}
          
          {onClearVectors && (
            <Tooltip title="Clear all vectors">
              <IconButton
                onClick={onClearVectors}
                size="small"
                sx={{ color: '#ef4444' }}
              >
                <Delete />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>

      {/* Summary Statistics */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="subtitle1" sx={{ color: '#10b981', mb: 2, fontWeight: 500 }}>
          Summary Statistics
        </Typography>
        
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
          <Chip
            label={`${completionPercentage}% Complete`}
            size="small"
            variant="outlined"
            sx={{ 
              borderColor: 'rgba(34, 197, 94, 0.3)', 
              color: '#22c55e',
              fontWeight: 600
            }}
          />
          <Chip
            label={`${stats.vectorizedChunks}/${stats.totalChunks} chunks`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(59, 130, 246, 0.3)', color: '#60a5fa' }}
          />
          <Chip
            label={`${stats.vectorizedFiles}/${stats.totalFiles} files`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(59, 130, 246, 0.3)', color: '#60a5fa' }}
          />
          <Chip
            label={`${stats.totalVectors.toLocaleString()} vectors`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(168, 85, 247, 0.3)', color: '#a855f7' }}
          />
          <Chip
            label={`${stats.embeddingDimensions}D embeddings`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(107, 114, 128, 0.3)', color: '#9ca3af' }}
          />
        </Box>

        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          <Chip
            label={`${(stats.indexingTime / 1000).toFixed(2)}s total time`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(234, 179, 8, 0.3)', color: '#facc15' }}
          />
          <Chip
            label={`${(stats.averageTimePerChunk * 1000).toFixed(0)}ms/chunk`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(234, 179, 8, 0.3)', color: '#facc15' }}
          />
          <Chip
            label={`${stats.provider}:${stats.model}`}
            size="small"
            variant="outlined"
            sx={{ borderColor: 'rgba(107, 114, 128, 0.3)', color: '#9ca3af' }}
          />
        </Box>
      </Box>

      {/* File Details */}
      {fileInfo.length > 0 && (
        <Box>
          <Button
            onClick={() => setShowFileDetails(!showFileDetails)}
            startIcon={showFileDetails ? <ExpandLess /> : <ExpandMore />}
            sx={{
              color: '#10b981',
              textTransform: 'none',
              fontWeight: 500,
              mb: 2
            }}
          >
            File Details ({fileInfo.length} files)
          </Button>

          <Collapse in={showFileDetails}>
            <TableContainer 
              sx={{ 
                maxHeight: 400,
                backgroundColor: 'rgba(34, 197, 94, 0.02)',
                border: '1px solid rgba(34, 197, 94, 0.1)',
                borderRadius: 1
              }}
            >
              <Table size="small" stickyHeader>
                <TableHead>
                  <TableRow>
                    <TableCell sx={{ color: '#10b981', fontWeight: 600 }}>File</TableCell>
                    <TableCell sx={{ color: '#10b981', fontWeight: 600 }}>Status</TableCell>
                    <TableCell sx={{ color: '#10b981', fontWeight: 600 }}>Chunks</TableCell>
                    <TableCell sx={{ color: '#10b981', fontWeight: 600 }}>Vectors</TableCell>
                    <TableCell sx={{ color: '#10b981', fontWeight: 600 }}>Last Updated</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {fileInfo.map((file, index) => (
                    <TableRow key={index}>
                      <TableCell sx={{ color: '#e2e8f0', maxWidth: 200 }}>
                        <Typography 
                          variant="body2" 
                          sx={{ 
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {file.file}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={getStatusLabel(file.status)}
                          size="small"
                          sx={{
                            backgroundColor: `${getStatusColor(file.status)}20`,
                            color: getStatusColor(file.status),
                            fontWeight: 600,
                            fontSize: '0.7rem'
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ color: '#94a3b8' }}>
                        {file.vectorizedChunks}/{file.chunks}
                      </TableCell>
                      <TableCell sx={{ color: '#94a3b8' }}>
                        {file.vectors.toLocaleString()}
                      </TableCell>
                      <TableCell sx={{ color: '#94a3b8' }}>
                        {file.lastVectorized 
                          ? new Date(file.lastVectorized).toLocaleString()
                          : 'Never'
                        }
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Collapse>
        </Box>
      )}

      {/* Last Updated */}
      <Box sx={{ mt: 2, pt: 2, borderTop: '1px solid rgba(34, 197, 94, 0.1)' }}>
        <Typography variant="caption" sx={{ color: '#64748b', display: 'flex', alignItems: 'center' }}>
          <Info sx={{ fontSize: 14, mr: 0.5 }} />
          Last updated: {new Date(stats.lastUpdated).toLocaleString()}
        </Typography>
      </Box>
    </Paper>
  );
};
