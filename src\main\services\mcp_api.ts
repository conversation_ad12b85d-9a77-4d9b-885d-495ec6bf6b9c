/**
 * @file src/main/services/mcp_api.ts
 * @description Local MCP-compliant API server using Express.
 * Provides context to AI coding tools.
 */

import express from 'express';

const PORT = 4888; // Port for the local MCP server

class MCPApiService {
  private app: express.Application;
  private server: any;

  constructor() {
    this.app = express();
    this.app.use(express.json());
    this.setupRoutes();
  }

  private setupRoutes() {
    this.app.get('/mcp/v1/status', (req, res) => {
      res.json({ status: 'running', timestamp: new Date().toISOString() });
    });

    this.app.post('/mcp/v1/context', async (req, res) => {
      const { question } = req.body;
      if (!question) {
        return res.status(400).json({ error: 'Missing "question" in request body' });
      }

      try {
        // Simplified response - RAG services have been removed
        res.json({
          context: `MCP API is running but RAG services have been removed. Question: ${question}`,
          sources: [],
          message: 'RAG functionality has been removed from this version'
        });
      } catch (error) {
        console.error('[MCP API] Error generating context:', error);
        res.status(500).json({
          error: 'Failed to generate context',
          details: error instanceof Error ? error.message : String(error)
        });
      }
    });

    // Legacy endpoints - functionality removed
    this.app.post('/mcp/v1/langchain/query', async (req, res) => {
      res.status(410).json({
        success: false,
        error: 'LangChain functionality has been removed',
        message: 'This endpoint is no longer available'
      });
    });

    this.app.get('/mcp/v1/langchain/stats/:projectId', async (req, res) => {
      res.status(410).json({
        success: false,
        error: 'LangChain functionality has been removed',
        message: 'This endpoint is no longer available'
      });
    });
  }

  start() {
    if (this.server) {
      console.log('MCP API server is already running.');
      return;
    }
    this.server = this.app.listen(PORT, '127.0.0.1', () => {
      console.log(`MCP API server started at http://127.0.0.1:${PORT}`);
    });
  }

  stop() {
    if (this.server) {
      this.server.close(() => {
        console.log('MCP API server stopped.');
        this.server = null;
      });
    }
  }
}

export const mcpApiService = new MCPApiService();
