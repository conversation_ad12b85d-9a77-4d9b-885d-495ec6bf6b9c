/**
 * @file src/main/managers/worker-manager.ts
 * @description Worker thread management for background processing.
 * LangChain workers have been removed - this is now a placeholder.
 */

import { WindowManager } from './window-manager';

export class WorkerManager {
  private windowManager: WindowManager;

  constructor(windowManager: WindowManager) {
    this.windowManager = windowManager;
  }

  /**
   * Create workers - LangChain functionality removed
   */
  createLangChainWorker(): void {
    console.log('[WorkerManager] LangChain worker functionality has been removed');
  }

  /**
   * Terminate all workers - placeholder
   */
  terminateAll(): void {
    console.log('[WorkerManager] No workers to terminate');
  }
}
