/**
 * @file src/main/managers/worker-manager.ts
 * @description Worker thread management for background processing.
 * Handles LangChain indexer worker lifecycle and communication.
 */

import { Worker } from 'worker_threads';
import path from 'path';
import { WindowManager } from './window-manager';

export class WorkerManager {
  private langChainWorker: Worker | null = null;
  private windowManager: WindowManager;

  constructor(windowManager: WindowManager) {
    this.windowManager = windowManager;
  }

  /**
   * Create and initialize LangChain worker
   */
  createLangChainWorker(): void {
    const workerPath = path.join(__dirname, 'langchain-indexer.worker.js');
    this.langChainWorker = new Worker(workerPath);
    
    this.langChainWorker.on('message', (result) => {
      console.log('[WorkerManager] Received from LangChain worker:', result);
      this.windowManager.sendToRenderer('langchain-indexing-update', result);
    });
    
    this.langChainWorker.on('error', (error) => {
      console.error('[WorkerManager] LangChain worker error:', error);
    });
    
    this.langChainWorker.on('exit', (code) => {
      if (code !== 0) {
        console.error(`[WorkerManager] LangChain worker stopped with exit code ${code}`);
      }
      this.langChainWorker = null;
    });

    console.log('[WorkerManager] LangChain worker created successfully');
  }

  /**
   * Get LangChain worker instance, create if not exists
   */
  getLangChainWorker(): Worker {
    if (!this.langChainWorker) {
      this.createLangChainWorker();
    }
    return this.langChainWorker!;
  }

  /**
   * Post message to LangChain worker
   */
  postToLangChainWorker(message: any): void {
    const worker = this.getLangChainWorker();
    worker.postMessage(message);
  }

  /**
   * Terminate all workers
   */
  terminateAll(): void {
    if (this.langChainWorker) {
      this.langChainWorker.terminate();
      this.langChainWorker = null;
      console.log('[WorkerManager] LangChain worker terminated');
    }
  }
}
