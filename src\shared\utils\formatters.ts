/**
 * @file formatters.ts
 * @description Utility functions for formatting numbers and file sizes
 */

/**
 * Format file size in bytes to human readable format
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  const value = bytes / Math.pow(k, i);
  
  if (value >= 100) {
    return `${Math.round(value)} ${sizes[i]}`;
  } else if (value >= 10) {
    return `${Math.round(value * 10) / 10} ${sizes[i]}`;
  } else {
    return `${Math.round(value * 100) / 100} ${sizes[i]}`;
  }
}

/**
 * Format large numbers to compact format (1.5K, 103M, etc.)
 */
export function formatCompactNumber(num: number): string {
  if (num === 0) return '0';
  
  const absNum = Math.abs(num);
  const sign = num < 0 ? '-' : '';
  
  if (absNum < 1000) {
    return sign + absNum.toString();
  }
  
  const units = [
    { value: 1e9, suffix: 'B' },
    { value: 1e6, suffix: 'M' },
    { value: 1e3, suffix: 'K' }
  ];
  
  for (const unit of units) {
    if (absNum >= unit.value) {
      const value = absNum / unit.value;
      
      if (value >= 100) {
        return sign + Math.round(value) + unit.suffix;
      } else if (value >= 10) {
        return sign + (Math.round(value * 10) / 10) + unit.suffix;
      } else {
        return sign + (Math.round(value * 100) / 100) + unit.suffix;
      }
    }
  }
  
  return sign + absNum.toString();
}

/**
 * Format character count to compact format
 */
export function formatCharCount(chars: number): string {
  return formatCompactNumber(chars) + ' chars';
}

/**
 * Format token count to compact format
 */
export function formatTokenCount(tokens: number): string {
  return formatCompactNumber(tokens) + ' tokens';
}

/**
 * Estimate token count from character count (rough approximation)
 * Average ratio is about 4 characters per token for English text
 */
export function estimateTokenCount(chars: number): number {
  return Math.round(chars / 4);
}

/**
 * Format file count to compact format
 */
export function formatFileCount(count: number): string {
  if (count === 0) return '0 files';
  if (count === 1) return '1 file';
  
  return formatCompactNumber(count) + ' files';
}
